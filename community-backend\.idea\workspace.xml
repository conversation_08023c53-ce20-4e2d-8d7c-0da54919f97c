<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="df83ed7c-de88-4601-843f-0ba6e6b069d6" name="变更" comment="">
      <change beforePath="$PROJECT_DIR$/src/main/resources/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/application.yml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="699ff6a66440b4a73119f466e1cfbfaf8d886de6" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="useMavenConfig" value="true" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectId" id="2yywTi2G0Hg31HedgWd4MC2ExJo" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/gitlab/vue-wxw&quot;,
    &quot;spring.configuration.checksum&quot;: &quot;24fade07924065e93d19125894ab31c5&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;mysql&quot;
    ]
  }
}</component>
  <component name="RunManager" selected="Spring Boot.XiaozuApplication">
    <configuration name="IDOCRTest.getAccessToken" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="xiaozu" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.hfut.xiaozu.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.hfut.xiaozu" />
      <option name="MAIN_CLASS_NAME" value="com.hfut.xiaozu.IDOCRTest" />
      <option name="METHOD_NAME" value="getAccessToken" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="IDOCRTest.getkey" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="xiaozu" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.hfut.xiaozu.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.hfut.xiaozu" />
      <option name="MAIN_CLASS_NAME" value="com.hfut.xiaozu.IDOCRTest" />
      <option name="METHOD_NAME" value="getkey" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="IDOCRTest.idcardLocalPic" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="xiaozu" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.hfut.xiaozu.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.hfut.xiaozu" />
      <option name="MAIN_CLASS_NAME" value="com.hfut.xiaozu.IDOCRTest" />
      <option name="METHOD_NAME" value="idcardLocalPic" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="OssTest.UploadFileLocal" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="xiaozu" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.hfut.xiaozu.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.hfut.xiaozu" />
      <option name="MAIN_CLASS_NAME" value="com.hfut.xiaozu.OssTest" />
      <option name="METHOD_NAME" value="UploadFileLocal" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="XiaozuApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <module name="xiaozu" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.hfut.xiaozu.XiaozuApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="JUnit.IDOCRTest.getAccessToken" />
        <item itemvalue="JUnit.OssTest.UploadFileLocal" />
        <item itemvalue="JUnit.IDOCRTest.idcardLocalPic" />
        <item itemvalue="JUnit.IDOCRTest.getkey" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="df83ed7c-de88-4601-843f-0ba6e6b069d6" name="变更" comment="" />
      <created>1750820131808</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1750820131808</updated>
      <workItem from="1750820133282" duration="232000" />
      <workItem from="1750820378627" duration="3960000" />
      <workItem from="1750837331343" duration="7434000" />
      <workItem from="1750862009612" duration="1846000" />
      <workItem from="1750900833747" duration="5899000" />
      <workItem from="1750935720103" duration="8773000" />
      <workItem from="1750985405109" duration="642000" />
      <workItem from="1750995293883" duration="4900000" />
      <workItem from="1751035510850" duration="634000" />
      <workItem from="1751096916322" duration="2541000" />
      <workItem from="1751183541529" duration="1261000" />
      <workItem from="1751267870460" duration="1481000" />
      <workItem from="1751275515267" duration="3162000" />
      <workItem from="1751332516184" duration="1025000" />
      <workItem from="1751352718727" duration="5716000" />
      <workItem from="1751417866411" duration="1635000" />
      <workItem from="1751424344801" duration="1019000" />
      <workItem from="1751441997779" duration="1643000" />
      <workItem from="1751461369673" duration="3089000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>