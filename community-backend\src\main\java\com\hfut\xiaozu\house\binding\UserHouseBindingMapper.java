package com.hfut.xiaozu.house.binding;

import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【user_house_binding(用户房屋绑定关系表（核心关系状态）)】的数据库操作Mapper
* @createDate 2025-06-26 17:25:28
* @Entity com.hfut.xiaozu.house.binding.UserHouseBinding
*/
@Mapper
public interface UserHouseBindingMapper {

    int insert(UserHouseBinding record);

    UserHouseBinding getById(Long id);

    int updateById(UserHouseBinding record);

    List<UserHouseBinding> listByStatus(Integer status);

    List<UserHouseBinding> getByUserId(Long userId);

    UserHouseBinding getByHouseId(Long l);

    List<UserHouseBinding> listByHouseIds(List<Long> houseIds);
}
