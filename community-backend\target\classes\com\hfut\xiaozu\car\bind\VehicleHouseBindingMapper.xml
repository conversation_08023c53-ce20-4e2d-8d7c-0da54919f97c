<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hfut.xiaozu.car.bind.VehicleHouseBindingMapper">

    <resultMap id="BaseResultMap" type="com.hfut.xiaozu.car.bind.VehicleHouseBindingEntity">
            <id property="id" column="id" />
            <result property="vehicleId" column="vehicle_id" />
            <result property="houseId" column="house_id" />
            <result property="status" column="status" />
            <result property="spaceNumber" column="space_number" />
            <result property="spaceType" column="space_type" />
            <result property="approvedBy" column="approved_by" />
            <result property="approvalRemark" column="approval_remark" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,vehicle_id,house_id,status,space_number,space_type,
        approved_by,approval_remark,create_time,update_time
    </sql>

    <select id="getById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from vehicle_house_binding
        where  id = #{id}
    </select>

    <select id="ListByVehicleId" resultType="com.hfut.xiaozu.car.bind.VehicleHouseBindingEntity">
        select
        <include refid="Base_Column_List" />
        from vehicle_house_binding
        where  vehicle_id = #{id}
    </select>

    <select id="listByStatus" resultType="com.hfut.xiaozu.car.bind.VehicleHouseBindingEntity">
        select
        <include refid="Base_Column_List" />
        from vehicle_house_binding
        where status  = #{status}
    </select>

    <select id="listByHouseIds" resultType="com.hfut.xiaozu.car.bind.VehicleHouseBindingEntity">
        select
        <include refid="Base_Column_List" />
        from vehicle_house_binding
        where house_id  in
        <foreach collection="houseIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="listByHouseIdsAndStatus" resultType="com.hfut.xiaozu.car.bind.VehicleHouseBindingEntity">
        select
        <include refid="Base_Column_List" />
        from vehicle_house_binding
        where house_id  in
        <foreach collection="houseIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND status = #{status}
    </select>

    <delete id="deleteById" parameterType="java.lang.Long">
        delete from vehicle_house_binding
        where  id = #{id}
    </delete>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.hfut.xiaozu.car.bind.VehicleHouseBindingEntity" useGeneratedKeys="true">
        insert into vehicle_house_binding
        ( vehicle_id,house_id,status,space_number,space_type,
        approved_by,approval_remark)
        values (#{vehicleId},#{houseId},#{status},#{spaceNumber},#{spaceType},
        #{approvedBy},#{approvalRemark})
    </insert>

    <update id="updateById" parameterType="com.hfut.xiaozu.car.bind.VehicleHouseBindingEntity">
        update vehicle_house_binding
        <set>
                <if test="vehicleId != null">
                    vehicle_id = #{vehicleId},
                </if>
                <if test="houseId != null">
                    house_id = #{houseId},
                </if>
                <if test="status != null">
                    status = #{status},
                </if>
                <if test="spaceNumber != null">
                    space_number = #{spaceNumber},
                </if>
                <if test="spaceType != null">
                    space_type = #{spaceType},
                </if>
                <if test="approvedBy != null">
                    approved_by = #{approvedBy},
                </if>
                <if test="approvalRemark != null">
                    approval_remark = #{approvalRemark},
                </if>
        </set>
        where   id = #{id}
    </update>

</mapper>
