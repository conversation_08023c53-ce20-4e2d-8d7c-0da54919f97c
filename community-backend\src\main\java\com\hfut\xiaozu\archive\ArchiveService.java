package com.hfut.xiaozu.archive;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.PageHelper;
import com.hfut.xiaozu.archive.vo.FamilyVO;
import com.hfut.xiaozu.archive.vo.HouseDetailVO;
import com.hfut.xiaozu.archive.vo.VehicleVO;
import com.hfut.xiaozu.car.bind.VehicleHouseBindingEntity;
import com.hfut.xiaozu.car.bind.VehicleHouseBindingMapper;
import com.hfut.xiaozu.car.info.VehicleInfoMapper;
import com.hfut.xiaozu.common.Result;
import com.hfut.xiaozu.family.info.FamilyMemberEntity;
import com.hfut.xiaozu.family.info.FamilyMemberMapper;
import com.hfut.xiaozu.house.binding.UserHouseBinding;
import com.hfut.xiaozu.house.binding.UserHouseBindingMapper;
import com.hfut.xiaozu.house.information.*;
import com.hfut.xiaozu.user.account.UserMapper;
import com.hfut.xiaozu.user.context.CurrentUser;
import com.hfut.xiaozu.user.context.UserContextHolder;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2025-07-02
 */
@Service
@Slf4j
public class ArchiveService {

    @Resource
    private HouseInfoMapper houseInfoMapper;

    @Resource
    private UserMapper userMapper;

    @Resource
    private CommunityInfoMapper communityInfoMapper;

    @Resource
    private BuildingInfoMapper buildingInfoMapper;

    @Resource
    private UnitInfoMapper unitInfoMapper;

    @Resource
    private UserHouseBindingMapper  userHouseBindingMapper;

    @Resource
    private FamilyMemberMapper familyMemberMapper;

    @Resource
    private VehicleHouseBindingMapper vehicleHouseBindingMapper;

    @Resource
    private VehicleInfoMapper vehicleInfoMapper;


    public Result<?> listHousesByCommunityId(Long communityId,Integer isOccupied, Integer pageNum, Integer pageSize) {
        CurrentUser currentUser = UserContextHolder.getCurrentUser();
        if(currentUser==null|| !Objects.equals(currentUser.getRole(),CurrentUser.Administrator)){
            return Result.fail("只有管理员才能查询社区人员");
        }

        CommunityInfo communityInfo = communityInfoMapper.selectById(communityId);
        if(communityInfo==null){
            return Result.fail("社区不存在");
        }

        List<HouseDetailVO> result=new ArrayList<>();
        PageHelper.startPage(pageNum, pageSize);
        List<HouseInfo> houseInfoList = houseInfoMapper.listByCommunityIdAndIsOccupied(communityId,isOccupied);
        for (HouseInfo houseInfo : houseInfoList) {
            HouseDetailVO vo = new HouseDetailVO();
            BeanUtil.copyProperties(houseInfo,vo);


            Long buildingId = buildingInfoMapper.getIdByHouseId(houseInfo.getId());
            BuildingInfo buildingInfo = buildingInfoMapper.getById(buildingId);

            HouseDetailVO.Community community = new HouseDetailVO.Community();
            BeanUtil.copyProperties(communityInfo,community);
            vo.setCommunity(community);

            if(buildingInfo!=null){
                HouseDetailVO.Building building = new HouseDetailVO.Building();
                BeanUtil.copyProperties(buildingInfo,building);
                vo.setBuilding(building);
            }

            Long unitId = houseInfo.getUnitId();
            UnitInfo unitInfo = unitInfoMapper.selectById(unitId);
            if(unitInfo!=null){
                HouseDetailVO.Unit unit = new HouseDetailVO.Unit();
                BeanUtil.copyProperties(unitInfo,unit);
                vo.setUnit(unit);
            }

            if(Objects.equals(isOccupied,1)) {
                UserHouseBinding userHouseBinding = userHouseBindingMapper.getByHouseId(houseInfo.getId());
                if (userHouseBinding != null && userHouseBinding.getStatus() == 2) {
                    HouseDetailVO.User user = new HouseDetailVO.User();
                    BeanUtil.copyProperties(userMapper.getById(userHouseBinding.getUserId()), user);
                    user.setRelation_type(userHouseBinding.getRelationType());
                    vo.setUser(user);
                }
            }
            result.add(vo);
        }

        return Result.ok(result);
    }

    public Result<?> listFamilyByCommunityId(Long communityId, Integer pageNum, Integer pageSize) {
        CurrentUser currentUser = UserContextHolder.getCurrentUser();
        if(currentUser==null|| !Objects.equals(currentUser.getRole(),CurrentUser.Administrator)){
            return Result.fail("只有管理员才能查询社区人员");
        }

        CommunityInfo communityInfo = communityInfoMapper.selectById(communityId);
        if(communityInfo==null){
            return Result.fail("社区不存在");
        }

        List<FamilyVO> result=new ArrayList<>();

        //查询社区内所有已绑定房屋的用户
        List<HouseInfo> houseIds = houseInfoMapper.listByCommunityIdAndIsOccupied(communityId, 1);


        if(CollectionUtil.isEmpty(houseIds)){
            log.error("社区id:{}",communityId);
            return Result.fail("该社区不存在有人住的屋子");
        }
        log.error("userIds: {}",houseIds);

        //查询这些用户的所有家属
        List<Long> userIds = userHouseBindingMapper.listByHouseIds
                (houseIds.stream().map(HouseInfo::getId).toList()).stream().map(UserHouseBinding::getUserId).toList();
        log.error("userIds: {}",userIds);
        userIds.forEach(
                userId->{
                    //查询家属
                    List<FamilyMemberEntity> familyMembers = familyMemberMapper.listByOwnerId(userId);
                    if(!CollectionUtil.isEmpty(familyMembers)){
                        familyMembers.forEach(
                                familyMember->{
                                    FamilyVO vo = new FamilyVO();
                                    vo.setOwnerId(userId);
                                    FamilyVO.Owner owner = new FamilyVO.Owner();
                                    BeanUtil.copyProperties(userMapper.getById(userId),owner);
                                    vo.setOwner(owner);

                                    BeanUtil.copyProperties(familyMember,vo);
                                    result.add(vo);
                                }
                        );
                    }
                }
        );

        if(CollectionUtil.isEmpty(result)){
            return Result.fail("不存在家属");
        }


        return Result.ok(result);
    }

    public Result<?> listVehiclesByCommunityId(Long communityId, Integer pageNum, Integer pageSize) {
        CurrentUser currentUser = UserContextHolder.getCurrentUser();
        if(currentUser==null|| !Objects.equals(currentUser.getRole(),CurrentUser.Administrator)){
            return Result.fail("只有管理员才能查询社区人员");
        }

        CommunityInfo communityInfo = communityInfoMapper.selectById(communityId);
        if(communityInfo==null){
            return Result.fail("社区不存在");
        }

        //查询社区内所有已绑定房屋
        List<Long> houseIds = houseInfoMapper.listByCommunityIdAndIsOccupied(communityId, 1)
                .stream().map(HouseInfo::getId).toList();

        if(CollectionUtil.isEmpty(houseIds)){
//            log.error("社区id:{}",communityId);
            return Result.fail("该社区不存在有人住的屋子");
        }

        //已经绑定成功的绑定记录的车id
        List<Long> vehicleIds = vehicleHouseBindingMapper.listByHouseIdsAndStatus(houseIds,2)
                .stream().map(VehicleHouseBindingEntity::getVehicleId).toList();

        if(CollectionUtil.isEmpty(vehicleIds)){
            return Result.fail("该社区房屋未绑定车辆");
        }

        List<VehicleVO> result=new ArrayList<>();

        vehicleIds.forEach(
                vehicleId->{
                    VehicleVO vo = new VehicleVO();
                    BeanUtil.copyProperties(vehicleInfoMapper.getById(vehicleId),vo);

                    List<VehicleHouseBindingEntity> h = vehicleHouseBindingMapper.ListByVehicleId(vehicleId);
                    if(CollectionUtil.isEmpty(h)){
                        return;
                    }
                    for (VehicleHouseBindingEntity vehicleHouseBindingEntity : h) {
                        vo.getHouseIds().add(vehicleHouseBindingEntity.getHouseId());
                        vo.setSpaceNumber(vehicleHouseBindingEntity.getSpaceNumber());
                        vo.setSpaceType(vehicleHouseBindingEntity.getSpaceType());
                    }
                    result.add(vo);
                }
        );

        if(CollectionUtil.isEmpty(result)){
            return Result.fail("该社区房屋未绑定车辆");
        }

        return Result.ok(result);
    }
}
