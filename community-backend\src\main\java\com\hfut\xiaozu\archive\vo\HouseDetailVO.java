package com.hfut.xiaozu.archive.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-07-02
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class HouseDetailVO {
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Community {
        /**
         * 主键ID
         */
        private Long id;

        /**
         * 小区名称
         */
        private String communityName;

        /**
         * 详细地址
         */
        private String addressDetail;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Building{
        /**
         * 主键ID
         */
        private Long id;

        /**
         * 楼栋编号
         */
        private String buildingCode;

        /**
         * 楼栋名称
         */
        private String buildingName;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Unit {
        /**
         * 主键ID
         */
        private Long id;

        /**
         * 单元编号
         */
        private String unitCode;

        /**
         * 楼层数
         */
        private Integer floorCount;

    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class User {

        private Long id;

        private String userName;

        private String phone;

        private String realName;

        private String idCard;

        /**
         * 是否实名认证（0-否 1-是）
         */
        private Integer isVerified;

        /**
         *关系类型: 1-业主 2-租客
         */
        private Integer relation_type;

    }

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 门牌号
     */
    private String houseNumber;

    /**
     * 房屋面积(m²)
     */
    private BigDecimal areaSize;

    /**
     * 是否已入住 0-否 1-是
     */
    private Integer isOccupied;

    private Community community;

    private Building building;

    private Unit unit;

    private User user;

}
