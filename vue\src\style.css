/* 治愈温馨暖黄色配色方案 - 替换原蓝色系 */
:root {
  /* 主色调 - 蓝白色系 */
  --primary-blue: #2196F3; /* 主蓝色 */
  --primary-blue-dark: #1976D2; /* 深蓝色 */
  --primary-blue-light: #64B5F6; /* 浅蓝色 */
  --primary-blue-lighter: #E3F2FD; /* 更浅蓝色 */

  /* 辅助橙色 */
  --secondary-orange: #FFAB40;
  --secondary-orange-dark: #FF9100;
  --secondary-orange-light: #FFE0B2;

  /* 中性色 - 白灰系 */
  --white: #FFFFFF;
  --light-gray: #F8F9FA;
  --gray: #E3F2FD;
  --medium-gray: #BBDEFB;
  --dark-gray: #90CAF9;
  --text-dark: #222;
  --text-light: #90CAF9;

  /* 点缀色 - 柔和绿 */
  --accent-green: #AED581;
  --accent-green-light: #DCE775;
  --accent-green-lighter: #F0F4C3;
  --accent-yellow: #FFD54F;
  --accent-yellow-light: #FFF59D;
  --accent-yellow-lighter: #FFFDE7;

  /* 状态色 */
  --success: var(--accent-green);
  --warning: #42A5F5;
  --error: #F44336;
  --info: var(--primary-blue);

  /* 全局蓝色色系变量 */
  --main-blue: #BBDEFB;
  --card-blue: #E3F2FD;
  --line-blue: #90CAF9;
  --btn-blue: #2196F3;
  --btn-blue-dark: #1976D2;
  --text-gray: #333;
}

body {
  color: var(--text-dark);
  background: #e3f2fd;
  background: linear-gradient(
    135deg,
    var(--white) 0%,
    var(--light-gray) 40%,
    var(--primary-blue-lighter) 70%,
    var(--white) 100%
  );
  min-height: 100vh;
}

/* 确保自定义弹窗容器在最顶层 */
#custom-dialog-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 10000;
}

#custom-dialog-container > * {
  pointer-events: auto;
}