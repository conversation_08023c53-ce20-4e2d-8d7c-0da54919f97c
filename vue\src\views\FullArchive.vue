<template>
  <div class="full-archive">
    <div class="page-header">
      <h1>全息档案</h1>
      <p class="subtitle">社区信息资源全面管理，辅助日常工作决策</p>
    </div>

    <div class="archive-content">
      <!-- 标签页导航 -->
      <div class="tab-navigation">
        <button
          v-for="tab in tabs"
          :key="tab.key"
          @click="switchTab(tab.key)"
          :class="['tab-btn', { active: activeTab === tab.key }]"
        >
          <span class="tab-icon">{{ tab.icon }}</span>
          <span class="tab-text">{{ tab.label }}</span>
        </button>
      </div>

      <!-- 标签页内容 -->
      <div class="tab-content">
        <!-- 人员档案 -->
        <div v-if="activeTab === 'personnel'" class="tab-panel">
          <div class="panel-header">
            <h2>人员档案管理</h2>
            <div class="panel-actions">
              <button @click="exportPersonnelData" class="export-btn" :disabled="isLoading || personnel.length === 0">
                <span class="btn-icon">📊</span>
                导出数据
              </button>
              <button @click="refreshPersonnelData" class="refresh-btn" :disabled="isLoading">
                <span class="btn-icon">🔄</span>
                {{ isLoading ? '刷新中...' : '刷新数据' }}
              </button>
            </div>
          </div>

          <!-- 筛选条件 -->
          <div class="filter-section">
            <div class="filter-row">
              <div class="filter-item">
                <label>关键词搜索</label>
                <input
                  v-model="personnelFilters.keyword"
                  @input="applyPersonnelFilters"
                  type="text"
                  placeholder="搜索姓名、身份证、电话..."
                  class="filter-input"
                />
              </div>

              <div class="filter-item">
                <label>选择社区</label>
                <select v-model="personnelFilters.communityId" @change="loadPersonnelData">
                  <option value="">全部社区</option>
                  <option
                    v-for="community in communities"
                    :key="community.communityId"
                    :value="community.communityId"
                  >
                    {{ community.communityName }}
                  </option>
                </select>
              </div>

              <div class="filter-item">
                <label>关系类型</label>
                <select v-model="personnelFilters.relationship" @change="applyPersonnelFilters">
                  <option value="">全部关系</option>
                  <option value="1">配偶</option>
                  <option value="2">子女</option>
                  <option value="3">父母</option>
                  <option value="4">其他</option>
                </select>
              </div>

              <div class="filter-actions">
                <button @click="resetPersonnelFilters" class="reset-btn">
                  <span class="btn-icon">🔄</span>
                  重置
                </button>
              </div>
            </div>
          </div>

          <!-- 加载状态 -->
          <div v-if="isLoading" class="loading-container">
            <div class="loading-spinner"></div>
            <p>正在加载人员档案数据...</p>
          </div>

          <!-- 错误提示 -->
          <div v-else-if="error" class="error-container">
            <p class="error-message">{{ error }}</p>
            <button @click="loadPersonnelData" class="retry-btn">重试</button>
          </div>

          <!-- 人员数据表格 -->
          <div v-else-if="personnel.length > 0" class="data-table-container">
            <div class="table-info">
              <span class="total-count">共找到 {{ personnelPagination.total }} 条人员记录</span>
            </div>

            <div class="table-wrapper">
              <table class="data-table">
                <thead>
                  <tr>
                    <th>姓名</th>
                    <th>身份证号</th>
                    <th>联系电话</th>
                    <th>关系类型</th>
                    <th>是否主要成员</th>
                    <th>业主信息</th>
                    <th>业主电话</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="person in personnel" :key="person.ownerId + '-' + person.memberName">
                    <td class="name-cell">
                      <strong>{{ person.memberName || '未填写' }}</strong>
                    </td>
                    <td class="id-card-cell">
                      {{ person.idCard || '未填写' }}
                    </td>
                    <td class="phone-cell">
                      {{ person.contactPhone || '未填写' }}
                    </td>
                    <td class="relationship-cell">
                      <span :class="['relationship-tag', 'relationship-' + person.relationship]">
                        {{ getRelationshipText(person.relationship) }}
                      </span>
                    </td>
                    <td class="primary-cell">
                      <span :class="['status-tag', person.isPrimary === 1 ? 'primary' : 'secondary']">
                        {{ person.isPrimary === 1 ? '主要成员' : '普通成员' }}
                      </span>
                    </td>
                    <td class="owner-cell">
                      <div class="owner-info">
                        <div class="owner-name">{{ person.owner?.realName || person.owner?.userName || '未知' }}</div>
                      </div>
                    </td>
                    <td class="owner-phone-cell">
                      {{ person.owner?.phone || '未填写' }}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            <!-- 分页控件 -->
            <div class="pagination-container">
              <div class="pagination-info">
                第 {{ personnelPagination.current }} 页，共 {{ personnelPagination.pages }} 页
              </div>
              <div class="pagination-controls">
                <button
                  @click="personnelFilters.pageNum = 1; loadPersonnelData()"
                  :disabled="personnelPagination.current === 1"
                  class="page-btn"
                >
                  首页
                </button>
                <button
                  @click="personnelFilters.pageNum--; loadPersonnelData()"
                  :disabled="personnelPagination.current === 1"
                  class="page-btn"
                >
                  上一页
                </button>
                <button
                  v-for="page in getPersonnelVisiblePages()"
                  :key="page"
                  @click="personnelFilters.pageNum = page; loadPersonnelData()"
                  :class="['page-btn', { active: page === personnelPagination.current }]"
                >
                  {{ page }}
                </button>
                <button
                  @click="personnelFilters.pageNum++; loadPersonnelData()"
                  :disabled="personnelPagination.current === personnelPagination.pages"
                  class="page-btn"
                >
                  下一页
                </button>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-else class="empty-state">
            <div class="empty-icon">👥</div>
            <h3 v-if="!personnelFilters.communityId && communities.length === 0">暂无社区数据</h3>
            <h3 v-else>暂无人员档案</h3>
            <p v-if="!personnelFilters.communityId && communities.length === 0">请先添加社区信息</p>
            <p v-else>当前筛选条件下没有找到人员记录</p>
          </div>
        </div>

        <!-- 房屋档案 -->
        <div v-if="activeTab === 'houses'" class="tab-panel">
          <div class="panel-header">
            <h2>房屋档案管理</h2>
            <div class="panel-actions">
              <button @click="exportHouseData" class="export-btn" :disabled="isLoading || houses.length === 0">
                <span class="btn-icon">📊</span>
                导出数据
              </button>
              <button @click="refreshHouseData" class="refresh-btn" :disabled="isLoading">
                <span class="btn-icon">🔄</span>
                {{ isLoading ? '刷新中...' : '刷新数据' }}
              </button>
            </div>
          </div>

          <!-- 筛选条件 -->
          <div class="filter-section">
            <div class="filter-row">
              <div class="filter-item">
                <label>关键词搜索：</label>
                <input
                  v-model="houseFilters.keyword"
                  type="text"
                  placeholder="搜索房屋编号、楼栋号、房间号..."
                  @keyup.enter="loadHouseData"
                  class="search-input"
                />
              </div>

              <div class="filter-item">
                <label>社区选择：</label>
                <select v-model="houseFilters.communityId" @change="loadHouseData">
                  <option value="">全部社区</option>
                  <option
                    v-for="community in communities"
                    :key="community.communityId"
                    :value="community.communityId"
                  >
                    {{ community.communityName }}
                  </option>
                </select>
              </div>

              <div class="filter-item">
                <label>绑定状态：</label>
                <select v-model="houseFilters.isOccupied" @change="loadHouseData">
                  <option value="">全部状态</option>
                  <option value="1">已绑定</option>
                  <option value="0">未绑定</option>
                </select>
              </div>

              <div class="filter-item">
                <button @click="searchHouseData" class="search-btn">
                  <span class="btn-icon">🔍</span>
                  搜索
                </button>
              </div>

              <div class="filter-item">
                <button @click="resetHouseFilters" class="reset-btn">
                  <span class="btn-icon">🔄</span>
                  重置筛选
                </button>
              </div>
            </div>
          </div>

          <!-- 加载状态 -->
          <div v-if="isLoading" class="loading-container">
            <div class="loading-spinner"></div>
            <p>正在加载房屋档案数据...</p>
          </div>

          <!-- 错误提示 -->
          <div v-else-if="error" class="error-container">
            <p class="error-message">{{ error }}</p>
            <button @click="loadHouseData" class="retry-btn">重试</button>
          </div>

          <!-- 房屋数据表格 -->
          <div v-else-if="houses.length > 0" class="data-table-container">
            <div class="table-info">
              <span class="total-count">共找到 {{ housePagination.total }} 条房屋记录</span>
            </div>
            
            <div class="table-wrapper">
              <table class="data-table">
                <thead>
                  <tr>
                    <th>房屋编号</th>
                    <th>社区名称</th>
                    <th>楼栋信息</th>
                    <th>单元信息</th>
                    <th>房屋面积</th>
                    <th>绑定状态</th>
                    <th>绑定用户</th>
                    <th>社区地址</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="house in houses" :key="house.id">
                    <td>{{ house.houseNumber || '-' }}</td>
                    <td>{{ house.community?.communityName || '-' }}</td>
                    <td>{{ house.building?.buildingName || '-' }}</td>
                    <td>{{ house.unit?.unitCode || '-' }}</td>
                    <td>{{ house.areaSize ? house.areaSize + '㎡' : '-' }}</td>
                    <td>
                      <span :class="['status-badge', house.isOccupied ? 'occupied' : 'vacant']">
                        {{ house.isOccupied ? '已绑定' : '未绑定' }}
                      </span>
                    </td>
                    <td>{{ house.user?.name || house.user?.username || '-' }}</td>
                    <td>{{ house.community?.addressDetail || '-' }}</td>
                  </tr>
                </tbody>
              </table>
            </div>

            <!-- 分页 -->
            <div class="pagination-container">
              <div class="pagination-info">
                第 {{ housePagination.current }} 页，共 {{ housePagination.pages }} 页
              </div>
              <div class="pagination-controls">
                <button
                  @click="changePage(housePagination.current - 1)"
                  :disabled="housePagination.current <= 1"
                  class="page-btn"
                >
                  上一页
                </button>
                <span class="page-numbers">
                  <button
                    v-for="page in getPageNumbers()"
                    :key="page"
                    @click="changePage(page)"
                    :class="['page-number', { active: page === housePagination.current }]"
                  >
                    {{ page }}
                  </button>
                </span>
                <button
                  @click="changePage(housePagination.current + 1)"
                  :disabled="housePagination.current >= housePagination.pages"
                  class="page-btn"
                >
                  下一页
                </button>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-else class="empty-state">
            <div class="empty-icon">🏠</div>
            <h3 v-if="!houseFilters.communityId && communities.length === 0">暂无社区数据</h3>
            <h3 v-else>暂无房屋档案</h3>
            <p v-if="!houseFilters.communityId && communities.length === 0">请先添加社区信息</p>
            <p v-else>当前筛选条件下没有找到房屋记录</p>
          </div>
        </div>

        <!-- 车辆档案 -->
        <div v-if="activeTab === 'vehicles'" class="tab-panel">
          <div class="panel-header">
            <h2>车辆档案管理</h2>
            <div class="panel-actions">
              <button @click="exportVehicleData" class="export-btn" :disabled="isLoading || vehicles.length === 0">
                <span class="btn-icon">📊</span>
                导出数据
              </button>
              <button @click="refreshVehicleData" class="refresh-btn" :disabled="isLoading">
                <span class="btn-icon">🔄</span>
                {{ isLoading ? '刷新中...' : '刷新数据' }}
              </button>
            </div>
          </div>

          <!-- 筛选条件 -->
          <div class="filter-section">
            <div class="filter-row">
              <div class="filter-item">
                <label>关键词搜索</label>
                <input
                  v-model="vehicleFilters.keyword"
                  @input="applyVehicleFilters"
                  type="text"
                  placeholder="搜索车牌号、品牌、颜色..."
                  class="filter-input"
                />
              </div>

              <div class="filter-item">
                <label>选择社区</label>
                <select v-model="vehicleFilters.communityId" @change="loadVehicleData">
                  <option value="">全部社区</option>
                  <option
                    v-for="community in communities"
                    :key="community.communityId"
                    :value="community.communityId"
                  >
                    {{ community.communityName }}
                  </option>
                </select>
              </div>

              <div class="filter-item">
                <label>车位类型</label>
                <select v-model="vehicleFilters.spaceType" @change="applyVehicleFilters">
                  <option value="">全部类型</option>
                  <option value="1">固定车位</option>
                  <option value="2">临时车位</option>
                </select>
              </div>

              <div class="filter-actions">
                <button @click="resetVehicleFilters" class="reset-btn">
                  <span class="btn-icon">🔄</span>
                  重置
                </button>
              </div>
            </div>
          </div>

          <!-- 加载状态 -->
          <div v-if="isLoading" class="loading-container">
            <div class="loading-spinner"></div>
            <p>正在加载车辆档案数据...</p>
          </div>

          <!-- 错误提示 -->
          <div v-else-if="error" class="error-container">
            <p class="error-message">{{ error }}</p>
            <button @click="loadVehicleData" class="retry-btn">重试</button>
          </div>

          <!-- 车辆数据表格 -->
          <div v-else-if="vehicles.length > 0" class="data-table-container">
            <div class="table-info">
              <span class="total-count">共找到 {{ vehiclePagination.total }} 条车辆记录</span>
            </div>

            <div class="table-wrapper">
              <table class="data-table">
                <thead>
                  <tr>
                    <th>车牌号</th>
                    <th>车辆品牌</th>
                    <th>车辆颜色</th>
                    <th>车位编号</th>
                    <th>车位类型</th>
                    <th>是否在社区</th>
                    <th>关联房屋</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="vehicle in vehicles" :key="vehicle.id">
                    <td class="plate-cell">
                      <strong>{{ vehicle.plateNumber || '未填写' }}</strong>
                    </td>
                    <td class="brand-cell">
                      {{ vehicle.brand || '未填写' }}
                    </td>
                    <td class="color-cell">
                      <span class="color-tag">{{ vehicle.color || '未填写' }}</span>
                    </td>
                    <td class="space-cell">
                      {{ vehicle.spaceNumber || '未分配' }}
                    </td>
                    <td class="space-type-cell">
                      <span :class="['space-type-tag', 'space-type-' + vehicle.spaceType]">
                        {{ getSpaceTypeText(vehicle.spaceType) }}
                      </span>
                    </td>
                    <td class="in-community-cell">
                      <span :class="['status-tag', vehicle.isInCommunity === 1 ? 'in-community' : 'out-community']">
                        {{ vehicle.isInCommunity === 1 ? '在社区内' : '不在社区' }}
                      </span>
                    </td>
                    <td class="house-cell">
                      <div class="house-info">
                        <span v-if="vehicle.houseIds && vehicle.houseIds.length > 0">
                          关联 {{ vehicle.houseIds.length }} 套房屋
                        </span>
                        <span v-else class="no-house">未关联房屋</span>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            <!-- 分页控件 -->
            <div class="pagination-container">
              <div class="pagination-info">
                第 {{ vehiclePagination.current }} 页，共 {{ vehiclePagination.pages }} 页
              </div>
              <div class="pagination-controls">
                <button
                  @click="vehicleFilters.pageNum = 1; loadVehicleData()"
                  :disabled="vehiclePagination.current === 1"
                  class="page-btn"
                >
                  首页
                </button>
                <button
                  @click="vehicleFilters.pageNum--; loadVehicleData()"
                  :disabled="vehiclePagination.current === 1"
                  class="page-btn"
                >
                  上一页
                </button>
                <button
                  v-for="page in getVehicleVisiblePages()"
                  :key="page"
                  @click="vehicleFilters.pageNum = page; loadVehicleData()"
                  :class="['page-btn', { active: page === vehiclePagination.current }]"
                >
                  {{ page }}
                </button>
                <button
                  @click="vehicleFilters.pageNum++; loadVehicleData()"
                  :disabled="vehiclePagination.current === vehiclePagination.pages"
                  class="page-btn"
                >
                  下一页
                </button>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-else class="empty-state">
            <div class="empty-icon">🚗</div>
            <h3 v-if="!vehicleFilters.communityId && communities.length === 0">暂无社区数据</h3>
            <h3 v-else>暂无车辆档案</h3>
            <p v-if="!vehicleFilters.communityId && communities.length === 0">请先添加社区信息</p>
            <p v-else>当前筛选条件下没有找到车辆记录</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { getToken } from '../utils/tokenManager.js';
import http from '../utils/httpInterceptor.js';

// 响应式数据
const activeTab = ref('houses'); // 默认显示房屋档案
const isLoading = ref(false);
const error = ref('');

// 标签页配置
const tabs = [
  { key: 'personnel', label: '人员档案', icon: '👥' },
  { key: 'houses', label: '房屋档案', icon: '🏠' },
  { key: 'vehicles', label: '车辆档案', icon: '🚗' }
];

// 社区数据
const communities = ref([]);

// 房屋档案相关数据
const allHouses = ref([]); // 存储所有房屋数据
const houses = ref([]); // 存储当前页显示的房屋数据
const houseFilters = reactive({
  keyword: '',
  communityId: '',
  isOccupied: '',
  pageNum: 1,
  pageSize: 10
});

// 人员档案相关数据
const allPersonnel = ref([]); // 存储所有人员数据
const personnel = ref([]); // 存储当前页显示的人员数据
const personnelFilters = reactive({
  keyword: '',
  communityId: '',
  relationship: '', // 关系类型筛选
  pageNum: 1,
  pageSize: 10
});

// 车辆档案相关数据
const allVehicles = ref([]); // 存储所有车辆数据
const vehicles = ref([]); // 存储当前页显示的车辆数据
const vehicleFilters = reactive({
  keyword: '',
  communityId: '',
  spaceType: '', // 车位类型筛选
  pageNum: 1,
  pageSize: 10
});

const housePagination = reactive({
  current: 1,
  pages: 1,
  total: 0,
  pageSize: 10
});

const personnelPagination = reactive({
  current: 1,
  pages: 1,
  total: 0,
  pageSize: 10
});

const vehiclePagination = reactive({
  current: 1,
  pages: 1,
  total: 0,
  pageSize: 10
});

// 获取社区信息
const loadCommunities = async () => {
  try {
    const token = getToken();
    if (!token) {
      throw new Error('未找到认证令牌，请重新登录');
    }

    const response = await http.get('/grids/communities');
    if (response && response.code === 200) {
      communities.value = response.data || [];
      console.log('✅ 社区信息加载成功:', communities.value);
    } else {
      throw new Error(response?.message || '获取社区信息失败');
    }
  } catch (err) {
    console.error('❌ 获取社区信息失败:', err);
    error.value = err.message || '获取社区信息失败';
  }
};

// 获取房屋档案数据
const loadHouseData = async () => {
  isLoading.value = true;
  error.value = '';

  try {
    const token = getToken();
    if (!token) {
      throw new Error('未找到认证令牌，请重新登录');
    }

    // 检查是否选择了社区（后端要求communityId为必需参数）
    if (!houseFilters.communityId) {
      // 如果有社区数据，默认选择第一个社区
      if (communities.value && communities.value.length > 0) {
        const firstCommunity = communities.value[0];
        if (firstCommunity && firstCommunity.communityId) {
          houseFilters.communityId = firstCommunity.communityId.toString();
        } else {
          console.warn('社区数据格式异常:', firstCommunity);
          allHouses.value = [];
          houses.value = [];
          housePagination.total = 0;
          isLoading.value = false;
          return;
        }
      } else {
        // 没有社区数据时，清空房屋列表并显示提示
        allHouses.value = [];
        houses.value = [];
        housePagination.total = 0;
        isLoading.value = false;
        return;
      }
    }

    // 构建请求参数
    const params = {
      communityId: parseInt(houseFilters.communityId)
    };

    // 添加可选参数
    if (houseFilters.isOccupied !== '') {
      params.isOccupied = parseInt(houseFilters.isOccupied);
    }

    console.log('🔧 请求房屋档案数据:', params);

    // 使用GET请求，参数作为查询参数
    const queryString = new URLSearchParams(params).toString();
    const response = await http.get(`/archive/houses/list?${queryString}`);

    if (response && response.code === 200) {
      // 根据实际返回的数据结构处理
      allHouses.value = response.data || [];

      // 应用前端筛选和分页
      applyFiltersAndPagination();

      console.log('✅ 房屋档案数据加载成功:', {
        total: allHouses.value.length,
        filtered: houses.value.length,
        pagination: housePagination
      });
    } else {
      throw new Error(response?.msg || response?.message || '获取房屋档案失败');
    }
  } catch (err) {
    console.error('❌ 获取房屋档案失败:', err);
    error.value = err.message || '获取房屋档案失败';
    allHouses.value = [];
    houses.value = [];
  } finally {
    isLoading.value = false;
  }
};

// 应用筛选和分页
const applyFiltersAndPagination = () => {
  let filteredHouses = [...allHouses.value];

  // 应用关键词搜索
  if (houseFilters.keyword.trim()) {
    const keyword = houseFilters.keyword.trim().toLowerCase();
    filteredHouses = filteredHouses.filter(house => {
      return (
        (house.houseNumber && house.houseNumber.toLowerCase().includes(keyword)) ||
        (house.building?.buildingName && house.building.buildingName.toLowerCase().includes(keyword)) ||
        (house.unit?.unitCode && house.unit.unitCode.toLowerCase().includes(keyword)) ||
        (house.community?.communityName && house.community.communityName.toLowerCase().includes(keyword))
      );
    });
  }

  // 计算分页
  const total = filteredHouses.length;
  const pageSize = houseFilters.pageSize;
  const current = houseFilters.pageNum;
  const startIndex = (current - 1) * pageSize;
  const endIndex = startIndex + pageSize;

  // 更新分页信息
  housePagination.current = current;
  housePagination.pages = Math.ceil(total / pageSize) || 1;
  housePagination.total = total;
  housePagination.pageSize = pageSize;

  // 获取当前页数据
  houses.value = filteredHouses.slice(startIndex, endIndex);
};

// 获取社区名称（现在直接从房屋数据中获取）
const getCommunityName = (communityId) => {
  const community = communities.value.find(c => c.id === communityId);
  return community ? community.name : '未知社区';
};

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-';
  try {
    return new Date(dateTime).toLocaleString('zh-CN');
  } catch {
    return '-';
  }
};

// 搜索房屋数据
const searchHouseData = () => {
  houseFilters.pageNum = 1;
  applyFiltersAndPagination();
};

// 重置房屋筛选条件
const resetHouseFilters = () => {
  houseFilters.keyword = '';
  // 保持社区选择，因为后端要求communityId为必需参数
  // houseFilters.communityId = '';
  houseFilters.isOccupied = '';
  houseFilters.pageNum = 1;
  loadHouseData();
};

// 刷新房屋数据
const refreshHouseData = () => {
  houseFilters.pageNum = 1;
  loadHouseData();
};

// 导出房屋数据
const exportHouseData = () => {
  // 获取所有筛选后的数据进行导出
  let exportData = [...allHouses.value];

  // 应用关键词搜索筛选
  if (houseFilters.keyword.trim()) {
    const keyword = houseFilters.keyword.trim().toLowerCase();
    exportData = exportData.filter(house => {
      return (
        (house.houseNumber && house.houseNumber.toLowerCase().includes(keyword)) ||
        (house.building?.buildingName && house.building.buildingName.toLowerCase().includes(keyword)) ||
        (house.unit?.unitCode && house.unit.unitCode.toLowerCase().includes(keyword)) ||
        (house.community?.communityName && house.community.communityName.toLowerCase().includes(keyword))
      );
    });
  }

  if (exportData.length === 0) {
    alert('暂无数据可导出');
    return;
  }

  // 构建CSV数据
  const headers = ['房屋编号', '社区名称', '楼栋信息', '单元信息', '房屋面积', '绑定状态', '绑定用户', '社区地址'];
  const csvData = [headers];

  exportData.forEach(house => {
    csvData.push([
      house.houseNumber || '-',
      house.community?.communityName || '-',
      house.building?.buildingName || '-',
      house.unit?.unitCode || '-',
      house.areaSize ? house.areaSize + '㎡' : '-',
      house.isOccupied ? '已绑定' : '未绑定',
      house.user?.name || house.user?.username || '-',
      house.community?.addressDetail || '-'
    ]);
  });

  // 转换为CSV格式
  const csvContent = csvData.map(row => row.join(',')).join('\n');

  // 创建下载链接
  const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  link.setAttribute('href', url);
  link.setAttribute('download', `房屋档案_${new Date().toISOString().slice(0, 10)}.csv`);
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);

  console.log('✅ 房屋档案数据导出成功，共导出', exportData.length, '条记录');
};

// 分页相关方法
const changePage = (page) => {
  if (page >= 1 && page <= housePagination.pages) {
    houseFilters.pageNum = page;
    applyFiltersAndPagination();
  }
};

const getPageNumbers = () => {
  const pages = [];
  const current = housePagination.current;
  const total = housePagination.pages;
  
  // 显示当前页前后2页
  const start = Math.max(1, current - 2);
  const end = Math.min(total, current + 2);
  
  for (let i = start; i <= end; i++) {
    pages.push(i);
  }
  
  return pages;
};

// 获取人员档案数据
const loadPersonnelData = async () => {
  try {
    isLoading.value = true;
    error.value = '';

    const token = getToken();
    if (!token) {
      throw new Error('未找到认证令牌，请重新登录');
    }

    // 检查是否选择了社区
    if (!personnelFilters.communityId) {
      // 如果有社区数据，默认选择第一个社区
      if (communities.value && communities.value.length > 0) {
        const firstCommunity = communities.value[0];
        if (firstCommunity && firstCommunity.communityId) {
          personnelFilters.communityId = firstCommunity.communityId.toString();
        } else {
          console.warn('社区数据格式异常:', firstCommunity);
          allPersonnel.value = [];
          personnel.value = [];
          personnelPagination.total = 0;
          isLoading.value = false;
          return;
        }
      } else {
        allPersonnel.value = [];
        personnel.value = [];
        personnelPagination.total = 0;
        isLoading.value = false;
        return;
      }
    }

    // 构建请求参数
    const params = {
      communityId: parseInt(personnelFilters.communityId),
      pageNum: personnelFilters.pageNum,
      pageSize: personnelFilters.pageSize
    };

    console.log('🔧 请求人员档案数据:', params);

    const response = await http.post('/archive/family/list', params);

    if (response && response.code === 200) {
      allPersonnel.value = response.data || [];
      applyPersonnelFilters();
      console.log('✅ 人员档案数据加载成功:', allPersonnel.value);
    } else {
      throw new Error(response?.msg || '获取人员档案数据失败');
    }
  } catch (err) {
    console.error('❌ 获取人员档案数据失败:', err);
    error.value = err.message || '获取人员档案数据失败';
    allPersonnel.value = [];
    personnel.value = [];
    personnelPagination.total = 0;
  } finally {
    isLoading.value = false;
  }
};

// 应用人员档案筛选条件
const applyPersonnelFilters = () => {
  let filteredData = [...allPersonnel.value];

  // 关键词搜索
  if (personnelFilters.keyword.trim()) {
    const keyword = personnelFilters.keyword.trim().toLowerCase();
    filteredData = filteredData.filter(person => {
      return (
        (person.memberName && person.memberName.toLowerCase().includes(keyword)) ||
        (person.idCard && person.idCard.toLowerCase().includes(keyword)) ||
        (person.contactPhone && person.contactPhone.includes(keyword)) ||
        (person.owner?.userName && person.owner.userName.toLowerCase().includes(keyword)) ||
        (person.owner?.realName && person.owner.realName.toLowerCase().includes(keyword))
      );
    });
  }

  // 关系类型筛选
  if (personnelFilters.relationship) {
    filteredData = filteredData.filter(person =>
      person.relationship === parseInt(personnelFilters.relationship)
    );
  }

  // 更新分页信息
  personnelPagination.total = filteredData.length;
  personnelPagination.pages = Math.ceil(filteredData.length / personnelFilters.pageSize);
  personnelPagination.current = personnelFilters.pageNum;

  // 分页处理
  const startIndex = (personnelFilters.pageNum - 1) * personnelFilters.pageSize;
  const endIndex = startIndex + personnelFilters.pageSize;
  personnel.value = filteredData.slice(startIndex, endIndex);
};

// 获取车辆档案数据
const loadVehicleData = async () => {
  try {
    isLoading.value = true;
    error.value = '';

    const token = getToken();
    if (!token) {
      throw new Error('未找到认证令牌，请重新登录');
    }

    // 检查是否选择了社区
    if (!vehicleFilters.communityId) {
      // 如果有社区数据，默认选择第一个社区
      if (communities.value && communities.value.length > 0) {
        const firstCommunity = communities.value[0];
        if (firstCommunity && firstCommunity.communityId) {
          vehicleFilters.communityId = firstCommunity.communityId.toString();
        } else {
          console.warn('社区数据格式异常:', firstCommunity);
          allVehicles.value = [];
          vehicles.value = [];
          vehiclePagination.total = 0;
          isLoading.value = false;
          return;
        }
      } else {
        allVehicles.value = [];
        vehicles.value = [];
        vehiclePagination.total = 0;
        isLoading.value = false;
        return;
      }
    }

    // 构建请求参数
    const params = {
      communityId: parseInt(vehicleFilters.communityId),
      pageNum: vehicleFilters.pageNum,
      pageSize: vehicleFilters.pageSize
    };

    console.log('🔧 请求车辆档案数据:', params);

    const response = await http.post('/archive/vehicles/list', params);

    if (response && response.code === 200) {
      allVehicles.value = response.data || [];
      applyVehicleFilters();
      console.log('✅ 车辆档案数据加载成功:', allVehicles.value);
    } else {
      throw new Error(response?.msg || '获取车辆档案数据失败');
    }
  } catch (err) {
    console.error('❌ 获取车辆档案数据失败:', err);
    error.value = err.message || '获取车辆档案数据失败';
    allVehicles.value = [];
    vehicles.value = [];
    vehiclePagination.total = 0;
  } finally {
    isLoading.value = false;
  }
};

// 应用车辆档案筛选条件
const applyVehicleFilters = () => {
  let filteredData = [...allVehicles.value];

  // 关键词搜索
  if (vehicleFilters.keyword.trim()) {
    const keyword = vehicleFilters.keyword.trim().toLowerCase();
    filteredData = filteredData.filter(vehicle => {
      return (
        (vehicle.plateNumber && vehicle.plateNumber.toLowerCase().includes(keyword)) ||
        (vehicle.brand && vehicle.brand.toLowerCase().includes(keyword)) ||
        (vehicle.color && vehicle.color.toLowerCase().includes(keyword)) ||
        (vehicle.spaceNumber && vehicle.spaceNumber.toLowerCase().includes(keyword))
      );
    });
  }

  // 车位类型筛选
  if (vehicleFilters.spaceType) {
    filteredData = filteredData.filter(vehicle =>
      vehicle.spaceType === parseInt(vehicleFilters.spaceType)
    );
  }

  // 更新分页信息
  vehiclePagination.total = filteredData.length;
  vehiclePagination.pages = Math.ceil(filteredData.length / vehicleFilters.pageSize);
  vehiclePagination.current = vehicleFilters.pageNum;

  // 分页处理
  const startIndex = (vehicleFilters.pageNum - 1) * vehicleFilters.pageSize;
  const endIndex = startIndex + vehicleFilters.pageSize;
  vehicles.value = filteredData.slice(startIndex, endIndex);
};

// 标签页切换方法
const switchTab = async (tabKey) => {
  activeTab.value = tabKey;

  // 根据切换的标签页加载相应数据
  if (tabKey === 'personnel' && personnel.value.length === 0) {
    await loadPersonnelData();
  } else if (tabKey === 'vehicles' && vehicles.value.length === 0) {
    await loadVehicleData();
  } else if (tabKey === 'houses' && houses.value.length === 0) {
    await loadHouseData();
  }
};

// 辅助方法
const getRelationshipText = (relationship) => {
  const relationshipMap = {
    1: '配偶',
    2: '子女',
    3: '父母',
    4: '其他'
  };
  return relationshipMap[relationship] || '未知';
};

const getSpaceTypeText = (spaceType) => {
  const spaceTypeMap = {
    1: '固定车位',
    2: '临时车位'
  };
  return spaceTypeMap[spaceType] || '未知';
};

// 人员档案相关方法
const refreshPersonnelData = () => {
  personnelFilters.pageNum = 1;
  loadPersonnelData();
};

const resetPersonnelFilters = () => {
  personnelFilters.keyword = '';
  personnelFilters.relationship = '';
  personnelFilters.pageNum = 1;
  applyPersonnelFilters();
};

const exportPersonnelData = () => {
  let exportData = [...allPersonnel.value];

  // 应用关键词搜索筛选
  if (personnelFilters.keyword.trim()) {
    const keyword = personnelFilters.keyword.trim().toLowerCase();
    exportData = exportData.filter(person => {
      return (
        (person.memberName && person.memberName.toLowerCase().includes(keyword)) ||
        (person.idCard && person.idCard.toLowerCase().includes(keyword)) ||
        (person.contactPhone && person.contactPhone.includes(keyword)) ||
        (person.owner?.userName && person.owner.userName.toLowerCase().includes(keyword)) ||
        (person.owner?.realName && person.owner.realName.toLowerCase().includes(keyword))
      );
    });
  }

  if (exportData.length === 0) {
    alert('暂无数据可导出');
    return;
  }

  // 构建CSV数据
  const headers = ['姓名', '身份证号', '联系电话', '关系类型', '是否主要成员', '业主姓名', '业主电话'];
  const csvData = [headers];

  exportData.forEach(person => {
    csvData.push([
      person.memberName || '未填写',
      person.idCard || '未填写',
      person.contactPhone || '未填写',
      getRelationshipText(person.relationship),
      person.isPrimary === 1 ? '主要成员' : '普通成员',
      person.owner?.realName || person.owner?.userName || '未知',
      person.owner?.phone || '未填写'
    ]);
  });

  // 下载CSV文件
  const csvContent = csvData.map(row => row.join(',')).join('\n');
  const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.download = `人员档案_${new Date().toISOString().slice(0, 10)}.csv`;
  link.click();
};

const getPersonnelVisiblePages = () => {
  const pages = [];
  const current = personnelPagination.current;
  const total = personnelPagination.pages;

  const start = Math.max(1, current - 2);
  const end = Math.min(total, current + 2);

  for (let i = start; i <= end; i++) {
    pages.push(i);
  }

  return pages;
};

// 车辆档案相关方法
const refreshVehicleData = () => {
  vehicleFilters.pageNum = 1;
  loadVehicleData();
};

const resetVehicleFilters = () => {
  vehicleFilters.keyword = '';
  vehicleFilters.spaceType = '';
  vehicleFilters.pageNum = 1;
  applyVehicleFilters();
};

const exportVehicleData = () => {
  let exportData = [...allVehicles.value];

  // 应用关键词搜索筛选
  if (vehicleFilters.keyword.trim()) {
    const keyword = vehicleFilters.keyword.trim().toLowerCase();
    exportData = exportData.filter(vehicle => {
      return (
        (vehicle.plateNumber && vehicle.plateNumber.toLowerCase().includes(keyword)) ||
        (vehicle.brand && vehicle.brand.toLowerCase().includes(keyword)) ||
        (vehicle.color && vehicle.color.toLowerCase().includes(keyword)) ||
        (vehicle.spaceNumber && vehicle.spaceNumber.toLowerCase().includes(keyword))
      );
    });
  }

  if (exportData.length === 0) {
    alert('暂无数据可导出');
    return;
  }

  // 构建CSV数据
  const headers = ['车牌号', '车辆品牌', '车辆颜色', '车位编号', '车位类型', '是否在社区', '关联房屋数量'];
  const csvData = [headers];

  exportData.forEach(vehicle => {
    csvData.push([
      vehicle.plateNumber || '未填写',
      vehicle.brand || '未填写',
      vehicle.color || '未填写',
      vehicle.spaceNumber || '未分配',
      getSpaceTypeText(vehicle.spaceType),
      vehicle.isInCommunity === 1 ? '在社区内' : '不在社区',
      vehicle.houseIds ? vehicle.houseIds.length : 0
    ]);
  });

  // 下载CSV文件
  const csvContent = csvData.map(row => row.join(',')).join('\n');
  const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.download = `车辆档案_${new Date().toISOString().slice(0, 10)}.csv`;
  link.click();
};

const getVehicleVisiblePages = () => {
  const pages = [];
  const current = vehiclePagination.current;
  const total = vehiclePagination.pages;

  const start = Math.max(1, current - 2);
  const end = Math.min(total, current + 2);

  for (let i = start; i <= end; i++) {
    pages.push(i);
  }

  return pages;
};

// 生命周期
onMounted(async () => {
  await loadCommunities();
  if (activeTab.value === 'houses') {
    await loadHouseData();
  } else if (activeTab.value === 'personnel') {
    await loadPersonnelData();
  } else if (activeTab.value === 'vehicles') {
    await loadVehicleData();
  }
});
</script>

<style scoped>
.full-archive {
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 30px;
  text-align: center;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 2.5em;
  font-weight: 600;
}

.subtitle {
  margin: 0;
  opacity: 0.9;
  font-size: 1.2em;
}

.archive-content {
  padding: 30px;
}

/* 标签页导航 */
.tab-navigation {
  display: flex;
  background: white;
  border-radius: 12px;
  padding: 8px;
  margin-bottom: 30px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.tab-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 16px 24px;
  border: none;
  background: transparent;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 16px;
  font-weight: 500;
  color: #6c757d;
}

.tab-btn:hover {
  background: #f8f9fa;
  color: #495057;
}

.tab-btn.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.tab-icon {
  font-size: 20px;
}

.tab-text {
  font-weight: 600;
}

/* 标签页内容 */
.tab-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.tab-panel {
  padding: 30px;
}

/* 面板头部 */
.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #e9ecef;
}

.panel-header h2 {
  margin: 0;
  color: #333;
  font-size: 1.8em;
  font-weight: 600;
}

.panel-actions {
  display: flex;
  gap: 12px;
}

.refresh-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: background 0.3s ease;
}

.refresh-btn:hover:not(:disabled) {
  background: #218838;
}

.refresh-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.export-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background: #17a2b8;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: background 0.3s ease;
}

.export-btn:hover:not(:disabled) {
  background: #138496;
}

.export-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.search-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 10px 16px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background 0.3s ease;
}

.search-btn:hover {
  background: #0056b3;
}

/* 筛选区域 */
.filter-section {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 30px;
}

.filter-row {
  display: flex;
  gap: 20px;
  align-items: end;
  flex-wrap: wrap;
}

.filter-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-item label {
  font-weight: 500;
  color: #495057;
  font-size: 14px;
}

.filter-item select,
.search-input {
  padding: 10px 12px;
  border: 1px solid #ced4da;
  border-radius: 6px;
  font-size: 14px;
  min-width: 150px;
  background: white;
}

.search-input {
  min-width: 200px;
}

.filter-item select:focus,
.search-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.reset-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 10px 16px;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background 0.3s ease;
}

.reset-btn:hover {
  background: #5a6268;
}

/* 加载和错误状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e9ecef;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-container p {
  font-size: 18px;
  color: #6c757d;
  margin: 0;
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.error-message {
  font-size: 18px;
  color: #dc3545;
  margin: 0 0 20px 0;
}

.retry-btn {
  padding: 12px 24px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.3s ease;
}

.retry-btn:hover {
  background: #0056b3;
}

/* 数据表格 */
.data-table-container {
  margin-top: 20px;
}

.table-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 0 4px;
}

.total-count {
  font-size: 14px;
  color: #6c757d;
  font-weight: 500;
}

.table-wrapper {
  overflow-x: auto;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
}

.data-table th {
  background: #f8f9fa;
  padding: 16px 12px;
  text-align: left;
  font-weight: 600;
  color: #495057;
  border-bottom: 2px solid #e9ecef;
  white-space: nowrap;
}

.data-table td {
  padding: 16px 12px;
  border-bottom: 1px solid #e9ecef;
  color: #333;
  white-space: nowrap;
}

.data-table tr:hover {
  background: #f8f9fa;
}

.data-table tr:last-child td {
  border-bottom: none;
}

/* 状态标签 */
.status-badge {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-align: center;
}

.status-badge.occupied {
  background: #d4edda;
  color: #155724;
}

.status-badge.vacant {
  background: #f8d7da;
  color: #721c24;
}

/* 分页 */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 30px;
  padding: 20px 0;
  border-top: 1px solid #e9ecef;
}

.pagination-info {
  font-size: 14px;
  color: #6c757d;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-btn {
  padding: 8px 16px;
  border: 1px solid #ced4da;
  background: white;
  color: #495057;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.page-btn:hover:not(:disabled) {
  background: #e9ecef;
  border-color: #adb5bd;
}

.page-btn:disabled {
  background: #f8f9fa;
  color: #6c757d;
  cursor: not-allowed;
  opacity: 0.6;
}

.page-numbers {
  display: flex;
  gap: 4px;
}

.page-number {
  padding: 8px 12px;
  border: 1px solid #ced4da;
  background: white;
  color: #495057;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
  min-width: 40px;
  text-align: center;
}

.page-number:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}

.page-number.active {
  background: #667eea;
  color: white;
  border-color: #667eea;
}

/* 空状态和即将推出 */
.empty-state,
.coming-soon {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  text-align: center;
}

.empty-icon,
.coming-soon-icon {
  font-size: 64px;
  margin-bottom: 20px;
  opacity: 0.6;
}

.empty-state h3,
.coming-soon h3 {
  margin: 0 0 12px 0;
  font-size: 24px;
  color: #495057;
}

.empty-state p,
.coming-soon p {
  margin: 0;
  font-size: 16px;
  color: #6c757d;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .archive-content {
    padding: 20px;
  }

  .tab-navigation {
    flex-direction: column;
    gap: 8px;
  }

  .tab-btn {
    justify-content: flex-start;
  }

  .panel-header {
    flex-direction: column;
    gap: 20px;
    align-items: flex-start;
  }

  .filter-row {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-item {
    width: 100%;
  }

  .filter-item select {
    min-width: auto;
    width: 100%;
  }

  .pagination-container {
    flex-direction: column;
    gap: 16px;
  }

  .page-numbers {
    flex-wrap: wrap;
    justify-content: center;
  }
}

.btn-icon {
  font-size: 16px;
}

/* 人员档案专用样式 */
.relationship-tag {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  color: white;
}

.relationship-1 { background: #e74c3c; } /* 配偶 - 红色 */
.relationship-2 { background: #3498db; } /* 子女 - 蓝色 */
.relationship-3 { background: #f39c12; } /* 父母 - 橙色 */
.relationship-4 { background: #95a5a6; } /* 其他 - 灰色 */

.status-tag.primary {
  background: #27ae60;
  color: white;
}

.status-tag.secondary {
  background: #bdc3c7;
  color: #2c3e50;
}

.name-cell strong {
  color: #2c3e50;
  font-weight: 600;
}

.id-card-cell {
  font-family: 'Courier New', monospace;
  font-size: 13px;
}

.phone-cell {
  font-family: 'Courier New', monospace;
}

.owner-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.owner-name {
  font-weight: 500;
  color: #2c3e50;
}

/* 车辆档案专用样式 */
.plate-cell strong {
  color: #2c3e50;
  font-weight: 600;
  font-family: 'Courier New', monospace;
}

.color-tag {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  background: #ecf0f1;
  color: #2c3e50;
}

.space-type-tag {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  color: white;
}

.space-type-1 { background: #27ae60; } /* 固定车位 - 绿色 */
.space-type-2 { background: #f39c12; } /* 临时车位 - 橙色 */

.status-tag.in-community {
  background: #27ae60;
  color: white;
}

.status-tag.out-community {
  background: #e74c3c;
  color: white;
}

.house-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.no-house {
  color: #95a5a6;
  font-style: italic;
}

.space-cell {
  font-family: 'Courier New', monospace;
  font-weight: 500;
}
</style>
