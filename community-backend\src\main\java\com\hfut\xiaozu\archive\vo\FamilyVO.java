package com.hfut.xiaozu.archive.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025-07-02
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class FamilyVO {

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Owner{

        private String userName;

        private String phone;

        private String realName;

        private String idCard;
    }

    private Owner owner;

    /**
     * 业主ID（关联user_account）
     */
    private Long ownerId;

    /**
     * 家人姓名
     */
    private String memberName;

    /**
     * 与业主关系:1-配偶 2-子女 3-父母 4-其他亲属
     */
    private Integer relationship;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 是否主要家庭成员 0-否 1-是
     */
    private Integer isPrimary;

}
