<template>
  <div class="register-page">
    <div class="register-main-container">
      <div class="register-image-container">
        <img src="/BA84B89F4F20BEF32B5061853BBC342F.jpg" alt="register-img" class="register-image" />
      </div>
      <div class="register-form-side">
        <div class="register-card">
          <div class="register-header">
            <div class="system-logo">🏡</div>
            <h1 class="system-title">芙蓉社区管理系统</h1>
            <p class="system-subtitle">Community Management System</p>
          </div>

          <div class="register-form-container">
            <h2 class="form-title">用户注册</h2>

            <!-- 错误和成功消息显示 -->
            <div v-if="errorMessage" class="message error-message">
              ❌ {{ errorMessage }}
            </div>
            <div v-if="successMessage" class="message success-message">
              ✅ {{ successMessage }}
            </div>

            <form @submit.prevent="handleRegister" class="register-form">
              <div class="form-group">
                <label for="userType" class="form-label">
                  <span class="label-icon">🏷️</span>
                  用户类型
                </label>
                <select
                  id="userType"
                  v-model="userType"
                  required
                  class="form-input"
                >
                  <option value="">请选择用户类型</option>
                  <option value="1">🏠 居民</option>
                  <option value="2">🔧 网格员</option>
                  <option value="3">👨‍💼 社区管理员</option>
                </select>
              </div>

              <div class="form-group">
                <label for="userName" class="form-label">
                  <span class="label-icon">👤</span>
                  用户名
                </label>
                <input
                  type="text"
                  id="userName"
                  v-model="userName"
                  required
                  class="form-input"
                  :class="{ 'error': userNameError }"
                  placeholder="请输入用户名（2-50个字符，不能为纯数字）"
                  @blur="handleUserNameBlur"
                  @input="handleUserNameInput"
                  maxlength="50"
                  autocomplete="username"
                >
                <div v-if="userNameError" class="field-error">
                  ❌ {{ userNameError }}
                </div>
                <div v-else-if="userName && userName.trim().length >= 2 && !userNameError" class="field-success">
                  ✅ 用户名格式正确
                </div>
                <div v-else-if="userName && userName.trim().length > 0 && userName.trim().length < 2" class="field-warning">
                  ⚠️ 用户名至少需要2个字符（当前{{ userName.trim().length }}个字符）
                </div>
                <div v-if="userName && userName.length > 0" class="field-info">
                  📝 字符数：{{ userName.trim().length }}/50
                </div>
              </div>

              <div class="form-group">
                <label for="phone" class="form-label">
                  <span class="label-icon">📱</span>
                  手机号码
                </label>
                <input
                  type="tel"
                  id="phone"
                  v-model="phone"
                  required
                  class="form-input"
                  :class="{ 'error': phoneError }"
                  placeholder="请输入手机号码（11位数字，以1开头）"
                  @blur="handlePhoneBlur"
                  @input="handlePhoneInput"
                  @keypress="handlePhoneKeypress"
                  maxlength="11"
                  pattern="[0-9]*"
                  inputmode="numeric"
                >
                <div v-if="phoneError" class="field-error">
                  ❌ {{ phoneError }}
                </div>
                <div v-else-if="phone && phone.length === 11 && !phoneError" class="field-success">
                  ✅ 手机号格式正确
                </div>
              </div>

              <div class="form-group">
                <label for="password" class="form-label">
                  <span class="label-icon">🔒</span>
                  密码
                </label>
                <input
                  type="password"
                  id="password"
                  v-model="password"
                  required
                  class="form-input"
                  placeholder="请输入密码"
                >
              </div>

              <div class="form-group">
                <label for="confirmPassword" class="form-label">
                  <span class="label-icon">🔐</span>
                  确认密码
                </label>
                <input
                  type="password"
                  id="confirmPassword"
                  v-model="confirmPassword"
                  required
                  class="form-input"
                  placeholder="请再次输入密码"
                >
              </div>

              <div class="form-group checkbox-group">
                <label class="checkbox-label">
                  <input type="checkbox" v-model="agreeTerms" required class="checkbox-input">
                  <span class="checkbox-custom"></span>
                  <span class="checkbox-text">
                    我已阅读并同意 <a href="#" class="terms-link">《用户协议》</a> 和 <a href="#" class="terms-link">《隐私政策》</a>
                  </span>
                </label>
              </div>

              <button type="submit" :disabled="isLoading || !agreeTerms" class="register-btn">
                <span v-if="isLoading" class="loading-spinner"></span>
                <span class="btn-text">{{ isLoading ? '注册中...' : '立即注册' }}</span>
              </button>

              <div class="form-footer">
                <p class="login-link">
                  已有账户？
                  <router-link to="/login" class="login-btn-link">立即登录</router-link>
                </p>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>

    <div class="register-info">
      <div class="info-card">
        <h3>🎯 注册须知</h3>
        <ul>
          <li>请选择正确的用户类型</li>
          <li>用户名不能为纯数字</li>
          <li>用户名将作为您的登录凭证</li>
          <li>密码长度至少6位字符</li>
          <li>请使用真实有效的手机号码</li>
        </ul>
      </div>
      <div class="info-card">
        <h3>🔐 安全保障</h3>
        <ul>
          <li>所有数据传输均采用加密技术</li>
          <li>严格保护用户隐私信息</li>
          <li>定期进行安全检查和更新</li>
          <li>24小时技术支持服务</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { authApi } from '../services/authApi.js';

const router = useRouter();

// 表单数据
const userType = ref('');
const userName = ref('');
const phone = ref('');
const password = ref('');
const confirmPassword = ref('');
const agreeTerms = ref(false);
const isLoading = ref(false);

// 错误和成功信息
const errorMessage = ref('');
const successMessage = ref('');
const phoneError = ref(''); // 手机号错误提示
const userNameError = ref(''); // 用户名错误提示

// 用户名验证函数（参照后端UserRegisterDTO约束）
const validateUserName = (username) => {
  if (!username) {
    return { valid: false, message: '用户名不能为空' };
  }

  // 去除首尾空格
  const trimmedUsername = username.trim();

  // 检查长度（2-50个字符）
  if (trimmedUsername.length < 2) {
    return { valid: false, message: '用户名长度至少2个字符' };
  }

  if (trimmedUsername.length > 50) {
    return { valid: false, message: '用户名长度不能超过50个字符' };
  }

  // 检查是否为纯数字（参照后端正则：^(?!\\d+$).+$）
  if (/^\d+$/.test(trimmedUsername)) {
    return { valid: false, message: '用户名不能为纯数字' };
  }

  // 检查是否为纯空白字符
  if (/^\s*$/.test(trimmedUsername)) {
    return { valid: false, message: '用户名不能为空白字符' };
  }

  // 检查是否包含不合适的字符（可选，根据业务需求调整）
  // 这里允许中文、英文、数字、下划线、连字符
  if (!/^[\u4e00-\u9fa5a-zA-Z0-9_-]+$/.test(trimmedUsername)) {
    return { valid: false, message: '用户名只能包含中文、英文、数字、下划线和连字符' };
  }

  return { valid: true, message: '用户名格式正确' };
};

// 手机号验证函数（参照后端PhoneUtil.isPhone()逻辑）
const validatePhone = (phoneNumber) => {
  if (!phoneNumber) {
    return { valid: false, message: '手机号不能为空' };
  }

  // 去除空格和特殊字符
  const cleanPhone = phoneNumber.replace(/\s+/g, '').replace(/[-()]/g, '');

  // 检查是否为纯数字
  if (!/^\d+$/.test(cleanPhone)) {
    return { valid: false, message: '手机号只能包含数字' };
  }

  // 检查长度
  if (cleanPhone.length !== 11) {
    if (cleanPhone.length < 11) {
      return { valid: false, message: `手机号不足11位，当前${cleanPhone.length}位` };
    } else {
      return { valid: false, message: `手机号超过11位，当前${cleanPhone.length}位` };
    }
  }

  // 检查是否以1开头
  if (!cleanPhone.startsWith('1')) {
    return { valid: false, message: '手机号必须以1开头' };
  }

  // 检查第二位是否为3-9
  const secondDigit = cleanPhone[1];
  if (!/[3-9]/.test(secondDigit)) {
    return { valid: false, message: '手机号第二位必须是3-9之间的数字' };
  }

  // 最终格式验证（与后端保持一致）
  if (!/^1[3-9]\d{9}$/.test(cleanPhone)) {
    return { valid: false, message: '手机号格式不正确' };
  }

  return { valid: true, message: '手机号格式正确' };
};

// 手机号输入完成后的验证
const handlePhoneBlur = () => {
  const validation = validatePhone(phone.value);
  if (!validation.valid) {
    phoneError.value = validation.message;
  } else {
    phoneError.value = '';
  }
};

// 手机号输入时的实时验证
const handlePhoneInput = () => {
  // 清除之前的错误提示
  if (phoneError.value) {
    phoneError.value = '';
  }

  // 如果输入了内容，进行基本格式检查
  if (phone.value) {
    const cleanPhone = phone.value.replace(/\s+/g, '').replace(/[-()]/g, '');

    // 检查是否包含非数字字符
    if (!/^\d*$/.test(cleanPhone)) {
      phoneError.value = '手机号只能包含数字';
      return;
    }

    // 检查长度（超过11位时提示）
    if (cleanPhone.length > 11) {
      phoneError.value = '手机号不能超过11位';
      return;
    }

    // 检查第一位（如果输入了第一位且不是1）
    if (cleanPhone.length >= 1 && !cleanPhone.startsWith('1')) {
      phoneError.value = '手机号必须以1开头';
      return;
    }

    // 检查第二位（如果输入了第二位且不是3-9）
    if (cleanPhone.length >= 2) {
      const secondDigit = cleanPhone[1];
      if (!/[3-9]/.test(secondDigit)) {
        phoneError.value = '手机号第二位必须是3-9之间的数字';
        return;
      }
    }
  }
};

// 用户名输入完成后的验证
const handleUserNameBlur = () => {
  const validation = validateUserName(userName.value);
  if (!validation.valid) {
    userNameError.value = validation.message;
  } else {
    userNameError.value = '';
  }
};

// 用户名输入时的实时验证
const handleUserNameInput = () => {
  // 清除之前的错误提示
  if (userNameError.value) {
    userNameError.value = '';
  }

  // 如果输入了内容，进行基本检查
  if (userName.value) {
    const trimmedUsername = userName.value.trim();

    // 检查长度（超过50个字符时提示）
    if (trimmedUsername.length > 50) {
      userNameError.value = '用户名长度不能超过50个字符';
      return;
    }

    // 检查是否为纯数字
    if (/^\d+$/.test(trimmedUsername)) {
      userNameError.value = '用户名不能为纯数字';
      return;
    }

    // 检查是否为纯空白字符
    if (/^\s+$/.test(userName.value)) {
      userNameError.value = '用户名不能为空白字符';
      return;
    }

    // 检查是否包含不合适的字符
    if (trimmedUsername && !/^[\u4e00-\u9fa5a-zA-Z0-9_-]*$/.test(trimmedUsername)) {
      userNameError.value = '用户名只能包含中文、英文、数字、下划线和连字符';
      return;
    }
  }
};

// 限制手机号只能输入数字
const handlePhoneKeypress = (event) => {
  // 允许退格、删除、Tab、Escape、Enter等控制键
  if ([8, 9, 27, 13, 46].indexOf(event.keyCode) !== -1 ||
      // 允许 Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X
      (event.keyCode === 65 && event.ctrlKey === true) ||
      (event.keyCode === 67 && event.ctrlKey === true) ||
      (event.keyCode === 86 && event.ctrlKey === true) ||
      (event.keyCode === 88 && event.ctrlKey === true)) {
    return;
  }

  // 确保输入的是数字
  if ((event.shiftKey || (event.keyCode < 48 || event.keyCode > 57)) && (event.keyCode < 96 || event.keyCode > 105)) {
    event.preventDefault();
  }
};

const handleRegister = async () => {
  // 清除之前的错误信息
  errorMessage.value = '';
  successMessage.value = '';

  // 前端验证
  if (!userType.value) {
    errorMessage.value = '请选择用户类型';
    return;
  }

  // 验证用户名
  const userNameValidation = validateUserName(userName.value);
  if (!userNameValidation.valid) {
    errorMessage.value = userNameValidation.message;
    userNameError.value = userNameValidation.message;
    return;
  }

  // 验证手机号
  const phoneValidation = validatePhone(phone.value);
  if (!phoneValidation.valid) {
    errorMessage.value = phoneValidation.message;
    phoneError.value = phoneValidation.message;
    return;
  }

  if (!password.value) {
    errorMessage.value = '请输入密码';
    return;
  }

  if (password.value !== confirmPassword.value) {
    errorMessage.value = '两次输入的密码不一致';
    return;
  }

  if (password.value.length < 6) {
    errorMessage.value = '密码长度至少6位字符';
    return;
  }

  if (!agreeTerms.value) {
    errorMessage.value = '请先同意用户协议和隐私政策';
    return;
  }

  isLoading.value = true;

  try {
    console.log('📝 开始注册流程...');

    // 构造请求数据（使用驼峰命名法）
    const userData = {
      userName: userName.value.trim(),
      phone: phone.value.trim(),
      password: password.value,
      userType: parseInt(userType.value)
    };

    console.log('🚀 发送注册请求:', userData);

    // 调用注册API
    const result = await authApi.register(userData);

    console.log('📦 注册结果:', result);

    if (result.success) {
      successMessage.value = result.message || '注册成功！';

      // 延迟跳转，让用户看到成功信息
      setTimeout(() => {
        router.push('/login');
      }, 2000);
    } else {
      errorMessage.value = result.message || '注册失败，请重试';
    }
  } catch (error) {
    console.error('❌ 注册失败:', error);
    errorMessage.value = error.message || '注册失败，请检查网络连接';
  } finally {
    isLoading.value = false;
  }
};
</script>

<style scoped>
.register-page {
  min-height: 100vh;
  width: 100vw;
  background: var(--primary-yellow-light);
  display: flex;
  align-items: stretch;
  justify-content: stretch;
}
.register-main-container {
  display: flex;
  width: 100vw;
  height: 100vh;
}
.register-image-container {
  flex: 0 0 61.8%;
  background: var(--primary-yellow);
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
}
.register-image {
  width: 100%;
  height: 100vh;
  object-fit: cover;
  border-radius: 0;
}
.register-form-side {
  flex: 1;
  background: var(--primary-yellow-light);
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
}
.register-card {
  background: var(--white);
  border-radius: 24px;
  box-shadow: 0 8px 32px rgba(255, 193, 7, 0.12);
  padding: 48px 40px;
  min-width: 400px;
  max-width: 480px;
  border: 1px solid var(--primary-yellow-dark);
}
.system-title {
  background: none;
  color: var(--primary-yellow-dark);
  -webkit-text-fill-color: initial;
}
.form-title::after {
  background: var(--primary-yellow-dark);
}
.register-btn {
  background: linear-gradient(135deg, var(--primary-yellow), var(--secondary-orange));
  color: var(--text-dark);
  border: none;
  border-radius: 12px;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 12px;
  box-shadow: 0 4px 16px rgba(255, 193, 7, 0.15);
}
.register-btn:hover:not(:disabled) {
  background: var(--primary-yellow-dark);
  color: var(--white);
}
.register-btn:active {
  background: var(--secondary-orange-dark);
  color: var(--white);
}
.message {
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
}
.error-message {
  background-color: #fee;
  color: #c53030;
  border: 1px solid #feb2b2;
}
.success-message {
  background-color: #f0fff4;
  color: #38a169;
  border: 1px solid #9ae6b4;
}
.form-group {
  position: relative;
}
.form-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  margin-bottom: 8px;
}
.label-icon {
  font-size: 16px;
}
.form-input {
  width: 100%;
  padding: 18px 22px;
  border: 2px solid var(--gray);
  border-radius: 12px;
  font-size: 18px;
  transition: all 0.3s ease;
  background: rgba(255,255,255,0.9);
  box-sizing: border-box;
}
.form-input:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
  background: white;
}
.form-input::placeholder {
  color: #adb5bd;
}
.form-input.error {
  border-color: #dc3545;
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}
.form-input.error:focus {
  border-color: #dc3545;
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.2);
}
.field-error {
  margin-top: 6px;
  font-size: 13px;
  color: #dc3545;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
}
.field-success {
  margin-top: 6px;
  font-size: 13px;
  color: #28a745;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
}
.field-warning {
  margin-top: 6px;
  font-size: 13px;
  color: #ffc107;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
}
.field-info {
  margin-top: 6px;
  font-size: 12px;
  color: #6c757d;
  font-weight: 400;
  display: flex;
  align-items: center;
  gap: 4px;
}
.checkbox-group {
  margin: 10px 0;
}
.checkbox-label {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  cursor: pointer;
  font-size: 14px;
  line-height: 1.5;
}
.checkbox-input {
  display: none;
}
.checkbox-custom {
  width: 20px;
  height: 20px;
  border: 2px solid #e9ecef;
  border-radius: 4px;
  background: white;
  position: relative;
  transition: all 0.3s ease;
  flex-shrink: 0;
  margin-top: 2px;
}
.checkbox-input:checked + .checkbox-custom {
  background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
  border-color: var(--primary-blue);
}
.checkbox-input:checked + .checkbox-custom::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}
.checkbox-text {
  color: #6c757d;
}
.terms-link {
  color: var(--primary-blue);
  text-decoration: none;
  font-weight: 500;
}
.terms-link:hover {
  text-decoration: underline;
}
.form-footer {
  text-align: center;
  margin-top: 20px;
}
.login-link {
  color: var(--text-light);
  font-size: 16px;
  margin: 0;
}
.login-btn-link {
  color: var(--accent-green);
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}
.login-btn-link:hover {
  color: var(--accent-green-light);
  text-decoration: underline;
}
.register-info {
  display: flex;
  flex-direction: column;
  gap: 20px;
  max-width: 350px;
}
.info-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  border: 1px solid rgba(255,255,255,0.2);
  transition: transform 0.3s ease;
}
.info-card:hover {
  transform: translateY(-5px);
}
.info-card h3 {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 15px 0;
  display: flex;
  align-items: center;
  gap: 10px;
}
.info-card ul {
  margin: 0;
  padding-left: 20px;
  color: #6c757d;
}
.info-card li {
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 8px;
}
.info-card li:last-child {
  margin-bottom: 0;
}
</style>