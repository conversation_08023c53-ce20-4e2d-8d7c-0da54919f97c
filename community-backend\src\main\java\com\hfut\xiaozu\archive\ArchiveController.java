package com.hfut.xiaozu.archive;

import com.hfut.xiaozu.common.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.models.security.SecurityScheme;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2025-07-02
 */
@Tag(name="档案查询")
@RestController
@RequestMapping("/api/archive")
public class ArchiveController {

    @Resource
    private ArchiveService archiveService;


    @Operation(summary = "查询社区所有房屋")
    @GetMapping("/houses/list")
    public Result<?> listHousesByCommunityId(@RequestParam Long communityId,
                                             @RequestParam(defaultValue = "1") Integer isOccupied,
                                             @RequestParam(defaultValue = "1") Integer pageNum,
                                             @RequestParam(defaultValue = "10") Integer pageSize
                                             ){
        return archiveService.listHousesByCommunityId(communityId,isOccupied,pageNum,pageSize);
    }

    @Operation(summary = "查询社区所有业主的家人")
    @GetMapping("/family/list")
    public Result<?> listfamilyByCommunityId(@RequestParam Long communityId,
                                             @RequestParam(defaultValue = "1") Integer pageNum,
                                             @RequestParam(defaultValue = "10") Integer pageSize
    ){
        return archiveService.listFamilyByCommunityId(communityId,pageNum,pageSize);
    }

    @Operation(summary = "查询社区所有车辆")
    @GetMapping("/vehicles/list")
    public Result<?> listVehiclesByCommunityId(@RequestParam Long communityId,
                                               @RequestParam(defaultValue = "1") Integer pageNum,
                                               @RequestParam(defaultValue = "10") Integer pageSize
    ){
        return archiveService.listVehiclesByCommunityId(communityId,pageNum,pageSize);
    }
}
