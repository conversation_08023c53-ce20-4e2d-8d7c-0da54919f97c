<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hfut.xiaozu.house.binding.UserHouseBindingMapper">

    <resultMap id="BaseResultMap" type="com.hfut.xiaozu.house.binding.UserHouseBinding">
            <id property="id" column="id" />
            <result property="userId" column="user_id" />
            <result property="houseId" column="house_id" />
            <result property="relationType" column="relation_type" />
            <result property="status" column="status" />
            <result property="submitTime" column="submit_time" />
            <result property="updateTime" column="update_time" />
            <result property="updatedBy" column="updated_by" />
            <result property="remark" column="remark" />
    </resultMap>

    <sql id="Base_Column_List">
        id,user_id,house_id,relation_type,status,submit_time,
        update_time,updated_by,remark
    </sql>

    <select id="getById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from user_house_binding
        where  id = #{id}
    </select>

    <select id="listByStatus" resultType="com.hfut.xiaozu.house.binding.UserHouseBinding">
        select
        <include refid="Base_Column_List" />
        from user_house_binding
        where  status = #{status}
    </select>

    <select id="getByUserId" resultType="com.hfut.xiaozu.house.binding.UserHouseBinding">
        select
        <include refid="Base_Column_List" />
        from user_house_binding
        where user_id  = #{userId}
    </select>

    <select id="getByHouseId" resultType="com.hfut.xiaozu.house.binding.UserHouseBinding">
        select
        <include refid="Base_Column_List" />
        from user_house_binding
        where house_id  = #{l}
    </select>

    <select id="listByHouseIds" resultType="com.hfut.xiaozu.house.binding.UserHouseBinding">
        select
        <include refid="Base_Column_List" />
        from user_house_binding
        where house_id  in
        <foreach collection="houseIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.hfut.xiaozu.house.binding.UserHouseBinding"
            useGeneratedKeys="true">
        insert into community.user_house_binding
        ( user_id,house_id,relation_type,status,updated_by,remark)
        values (#{userId},#{houseId},#{relationType},#{status},#{updatedBy},#{remark})
    </insert>


    <update id="updateById" parameterType="com.hfut.xiaozu.house.binding.UserHouseBinding">
        update community.user_house_binding
        <set>
                <if test="status != null">
                    status = #{status},
                </if>
                <if test="updatedBy != null">
                    updated_by = #{updatedBy},
                </if>
                <if test="remark != null">
                    remark = #{remark},
                </if>
        </set>
        where   id = #{id}
    </update>

</mapper>
