<template>
  <div class="resident-home" :class="{ 'elderly-mode': isElderlyMode }">
    <!-- 老年版切换按钮 -->
    <div class="mode-toggle">
      <button
        class="toggle-btn"
        @click="toggleElderlyMode"
        :class="{ 'active': isElderlyMode }"
      >
        <span class="toggle-icon">{{ isElderlyMode ? '👴' : '👁️' }}</span>
        <span class="toggle-text">{{ isElderlyMode ? '标准版' : '老年版' }}</span>
      </button>
    </div>

    <div class="dashboard-header">
      <h1>居民端</h1>
      <p class="subtitle">社区服务系统 - 便民服务与功能导航</p>
    </div>

    <div class="dashboard-content">
      <!-- 个人信息概览 -->
      <section class="profile-section">
        <h2 class="section-title">个人信息概览</h2>
        <div v-if="isLoading" class="loading-container">
          <div class="loading-spinner"></div>
          <p>正在加载个人信息...</p>
        </div>
        <div v-else-if="error" class="error-container">
          <p class="error-message">{{ error }}</p>
          <button @click="loadUserInfo" class="retry-btn">重试</button>
        </div>
        <div v-else class="profile-grid">
          <div class="profile-card">
            <div class="profile-icon">🆔</div>
            <div class="profile-info">
              <h3>认证状态</h3>
              <p class="status-text" :class="userInfo.verificationStatus.class">
                {{ userInfo.verificationStatus.text }}
              </p>
              <span class="status-date">{{ userInfo.verificationStatus.date }}</span>
            </div>
          </div>

          <div class="profile-card">
            <div class="profile-icon">🏠</div>
            <div class="profile-info">
              <h3>绑定房屋</h3>
              <p class="status-text" :class="userInfo.houseInfo.class">
                {{ userInfo.houseInfo.text }}
              </p>
              <span class="status-date">{{ userInfo.houseInfo.date }}</span>
            </div>
          </div>

          <div class="profile-card">
            <div class="profile-icon">🚗</div>
            <div class="profile-info">
              <h3>车辆信息</h3>
              <p class="status-text" :class="userInfo.vehicleInfo.class">
                {{ userInfo.vehicleInfo.text }}
              </p>
              <span class="status-date">{{ userInfo.vehicleInfo.date }}</span>
            </div>
          </div>

          <div class="profile-card">
            <div class="profile-icon">👨‍👩‍👧‍👦</div>
            <div class="profile-info">
              <h3>家庭成员</h3>
              <p class="status-text" :class="userInfo.familyInfo.class">
                {{ userInfo.familyInfo.text }}
              </p>
              <span class="status-date">{{ userInfo.familyInfo.date }}</span>
            </div>
          </div>
        </div>
      </section>

      <!-- 居民端功能模块 -->
      <section class="function-section">
        <h2 class="section-title">居民服务功能</h2>
        <div class="grid-container">
          <div class="grid-item" @click="navigateTo('/resident/real-name-auth')">
            <div class="item-icon">🆔</div>
            <h3>实名认证</h3>
            <p>表单填写, OCR识别, 认证状态管理, 真实性校验</p>
          </div>

          <div class="grid-item" @click="navigateTo('/resident/house-management')">
            <div class="item-icon">🏠</div>
            <h3>房屋管理</h3>
            <p>房屋绑定, 二维码绑定, 档案信息同步, 绑定审核</p>
          </div>

          <div class="grid-item" @click="navigateTo('/resident/vehicle-management')">
            <div class="item-icon">🚗</div>
            <h3>车辆管理</h3>
            <p>车辆车位绑定, 车辆房屋绑定, 车辆信息管理, 车辆验证</p>
          </div>

          <div class="grid-item" @click="navigateTo('/resident/family-management')">
            <div class="item-icon">👨‍👩‍👧‍👦</div>
            <h3>家人管理</h3>
            <p>家人信息录入, 家人绑定业主</p>
          </div>

          <div class="grid-item" @click="navigateTo('/resident/issue-report')">
            <div class="item-icon">📢</div>
            <h3>问题上报</h3>
            <p>问题反馈, 事件分类, 事件流转与处理跟踪, 进度通知</p>
          </div>

          <div class="grid-item" @click="navigateTo('/resident/budget-vote')">
            <div class="item-icon">🗳️</div>
            <h3>预算支出投票</h3>
            <p>投票管理, 投票结果展示, 投票鉴权与投票</p>
          </div>
        </div>
      </section>

      <!-- 公共模块 -->
      <section class="function-section">
        <h2 class="section-title">公共模块</h2>
        <div class="grid-container">
          <!-- 已删除四个无用模块：用户与权限服务、文件存储服务、消息通知服务、日志与监控 -->

          <div class="grid-item" @click="navigateTo('/common/voice-demo')">
            <div class="item-icon">🎤</div>
            <h3>语音文字互转</h3>
            <p>文字转语音, 语音转文字, TTS演示</p>
          </div>
        </div>
      </section>

      <!-- 快速操作区域 -->
      <section class="quick-actions">
        <h2 class="section-title">快速操作</h2>
        <div class="action-buttons">
          <button class="action-btn primary" @click="navigateTo('/resident/issue-report')">
            <span class="btn-icon">📢</span>
            问题上报
          </button>
          <button class="action-btn secondary" @click="navigateTo('/resident/budget-vote')">
            <span class="btn-icon">🗳️</span>
            参与投票
          </button>
          <button class="action-btn tertiary" @click="navigateTo('/resident/house-management')">
            <span class="btn-icon">🏠</span>
            房屋信息
          </button>
        </div>
      </section>

      <!-- 社区公告 -->
      <section class="announcements">
        <h2 class="section-title">社区公告</h2>
        <div class="announcement-list">
          <div class="announcement-item">
            <div class="announcement-date">2024-06-19</div>
            <div class="announcement-content">
              <h4>关于小区停车位管理的通知</h4>
              <p>为了更好地管理小区停车位，请各位业主及时绑定车辆信息...</p>
            </div>
          </div>
          <div class="announcement-item">
            <div class="announcement-date">2024-06-18</div>
            <div class="announcement-content">
              <h4>社区绿化改造投票开始</h4>
              <p>关于小区绿化改造方案的投票已经开始，请各位业主积极参与...</p>
            </div>
          </div>
          <div class="announcement-item">
            <div class="announcement-date">2024-06-17</div>
            <div class="announcement-content">
              <h4>物业费缴费提醒</h4>
              <p>本月物业费缴费截止日期为月底，请各位业主及时缴费...</p>
            </div>
          </div>
        </div>
      </section>
    </div>

    <!-- 语音助手 -->
    <VoiceAssistant ref="voiceAssistant" />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import VoiceAssistant from '../components/VoiceAssistant.vue';
import { getToken } from '../utils/tokenManager.js';
import { getMyVehicles } from '../services/vehicleApi.js';
import { getMyFamilyMembers } from '../services/familyApi.js';

const router = useRouter();

// 老年版模式状态
const isElderlyMode = ref(false);

// 数据加载状态
const isLoading = ref(false);
const error = ref('');

// 用户信息
const userInfo = reactive({
  verificationStatus: {
    text: '未认证',
    class: 'pending',
    date: '请完成实名认证'
  },
  houseInfo: {
    text: '未绑定',
    class: 'pending',
    date: '请绑定房屋信息'
  },
  vehicleInfo: {
    text: '未绑定',
    class: 'pending',
    date: '请添加车辆信息'
  },
  familyInfo: {
    text: '未添加',
    class: 'pending',
    date: '请添加家庭成员'
  }
});

// 从localStorage读取用户偏好
onMounted(() => {
  const savedMode = localStorage.getItem('elderlyMode');
  if (savedMode === 'true') {
    isElderlyMode.value = true;
  }
  // 加载用户信息
  loadUserInfo();
});

// 切换老年版模式
const toggleElderlyMode = () => {
  isElderlyMode.value = !isElderlyMode.value;
  // 保存用户偏好到localStorage
  localStorage.setItem('elderlyMode', isElderlyMode.value.toString());
};

const navigateTo = (path) => {
  console.log(`🔗 导航到: ${path}`);
  router.push(path);
};

// 加载用户信息
const loadUserInfo = async () => {
  isLoading.value = true;
  error.value = '';

  try {
    const token = getToken();
    if (!token) {
      throw new Error('未找到认证令牌，请重新登录');
    }

    // 并行获取各种信息
    await Promise.all([
      fetchUserBasicInfo(),
      fetchVerificationStatus(),
      fetchHouseInfo(),
      fetchVehicleInfo(),
      fetchFamilyInfo()
    ]);

    console.log('✅ 用户信息加载完成');
  } catch (err) {
    console.error('❌ 用户信息加载失败:', err);
    error.value = err.message || '加载用户信息失败';
  } finally {
    isLoading.value = false;
  }
};

// 获取用户基本信息
const fetchUserBasicInfo = async () => {
  try {
    const token = getToken();
    const response = await fetch('http://localhost:8080/api/auth/me', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();
    console.log('✅ 用户基本信息:', result);
  } catch (err) {
    console.error('❌ 获取用户基本信息失败:', err);
  }
};

// 获取实名认证状态
const fetchVerificationStatus = async () => {
  try {
    const token = getToken();
    const response = await fetch('http://localhost:8080/api/auth/identity/me', {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();
    console.log('✅ 实名认证状态:', result);

    if (result.code === 200 && result.data) {
      const status = result.data.status;
      if (status === 1) {
        userInfo.verificationStatus = {
          text: '审核中',
          class: 'pending',
          date: '等待审核结果'
        };
      } else if (status === 2) {
        userInfo.verificationStatus = {
          text: '已认证',
          class: 'verified',
          date: '认证通过'
        };
      } else if (status === 3) {
        userInfo.verificationStatus = {
          text: '审核失败',
          class: 'error',
          date: '请重新提交'
        };
      }
    } else {
      // 未提交认证申请
      userInfo.verificationStatus = {
        text: '未认证',
        class: 'pending',
        date: '请完成实名认证'
      };
    }
  } catch (err) {
    console.error('❌ 获取实名认证状态失败:', err);
    userInfo.verificationStatus = {
      text: '未认证',
      class: 'pending',
      date: '请完成实名认证'
    };
  }
};

// 获取房屋绑定信息
const fetchHouseInfo = async () => {
  try {
    const token = getToken();
    const response = await fetch('http://localhost:8080/api/grids/houses/binding/me', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();
    console.log('✅ 房屋绑定信息:', result);

    if (result.code === 200 && result.data && result.data.length > 0) {
      const house = result.data[0];

      if (house.status === 2) {
        // 只有审核通过的才显示为已绑定
        userInfo.houseInfo = {
          text: '已绑定',
          class: 'verified',
          date: '认证通过'
        };
      } else if (house.status === 1) {
        userInfo.houseInfo = {
          text: '审核中',
          class: 'pending',
          date: '等待审核结果'
        };
      } else if (house.status === 3) {
        userInfo.houseInfo = {
          text: '审核失败',
          class: 'error',
          date: '请重新申请'
        };
      } else {
        userInfo.houseInfo = {
          text: '未绑定',
          class: 'pending',
          date: '请绑定房屋信息'
        };
      }
    } else {
      userInfo.houseInfo = {
        text: '未绑定',
        class: 'pending',
        date: '请绑定房屋信息'
      };
    }
  } catch (err) {
    console.error('❌ 获取房屋绑定信息失败:', err);
    userInfo.houseInfo = {
      text: '未绑定',
      class: 'pending',
      date: '请绑定房屋信息'
    };
  }
};

// 获取车辆信息
const fetchVehicleInfo = async () => {
  try {
    const response = await getMyVehicles();
    console.log('✅ 车辆信息:', response);

    if (response.success && response.data && response.data.length > 0) {
      const vehicleCount = response.data.length;
      const firstVehicle = response.data[0];

      userInfo.vehicleInfo = {
        text: vehicleCount === 1 ? firstVehicle.licensePlate : `${vehicleCount}辆车`,
        class: 'verified',
        date: vehicleCount === 1 ? (firstVehicle.parkingSpace || '已添加') : '已添加'
      };
    } else {
      userInfo.vehicleInfo = {
        text: '未绑定',
        class: 'pending',
        date: '请添加车辆信息'
      };
    }
  } catch (err) {
    console.error('❌ 获取车辆信息失败:', err);
    userInfo.vehicleInfo = {
      text: '未绑定',
      class: 'pending',
      date: '请添加车辆信息'
    };
  }
};

// 获取家庭成员信息
const fetchFamilyInfo = async () => {
  try {
    const response = await getMyFamilyMembers();
    console.log('✅ 家庭成员信息:', response);

    if (response.success && response.data && response.data.length > 0) {
      const memberCount = response.data.length;

      userInfo.familyInfo = {
        text: `${memberCount}位成员`,
        class: 'verified',
        date: '已添加'
      };
    } else {
      userInfo.familyInfo = {
        text: '未添加',
        class: 'pending',
        date: '请添加家庭成员'
      };
    }
  } catch (err) {
    console.error('❌ 获取家庭成员信息失败:', err);
    userInfo.familyInfo = {
      text: '未添加',
      class: 'pending',
      date: '请添加家庭成员'
    };
  }
};
</script>

<style scoped>
.resident-home {
  background: var(--primary-yellow-light);
  min-height: 100vh;
}

/* 模式切换按钮样式 */
.mode-toggle {
  position: fixed;
  top: 80px;
  right: 20px;
  z-index: 1000;
}

.toggle-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: blue;
  border: 2px solid blue;
  border-radius: 25px;
  color: blue;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(1, 5, 255, 0.2);
}

.toggle-btn:hover {
  background: blue;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(55, 0, 255, 0.3);
}

.toggle-btn.active {
  background: blue;
  border-color: blue;
  color: white;
}

.toggle-btn.active:hover {
  background: blue;
  border-color: blue;
}

.toggle-icon {
  font-size: 18px;
}

.toggle-text {
  font-size: 14px;
}

.dashboard-header {
  text-align: center;
  margin-bottom: 40px;
  padding: 30px 0;
  background: linear-gradient(135deg,rgb(34, 170, 255),white );
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(19, 6, 210, 0.08);
}

.dashboard-header h1 {
  font-size: 32px;
  color: blue;
  margin: 0 0 10px 0;
  font-weight: 700;
}

.subtitle {
  font-size: 16px;
  color: #333;
  margin: 0;
}

/* 老年版模式样式 */
.elderly-mode .dashboard-header h1 {
  font-size: 48px;
}

.elderly-mode .subtitle {
  font-size: 24px;
  line-height: 1.8;
}

.dashboard-content {
  display: flex;
  flex-direction: column;
  gap: 40px;
  background: #fff;
}

.profile-section,
.function-section,
.quick-actions,
.announcements {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 15px #fff;
  margin-bottom: 24px;
}

.section-title {
  border-bottom: 2px solid rgb(34, 170, 255);
  color: rgb(34, 170, 255);
}

.profile-card,
.grid-item {
  background: rgb(34, 170, 255);
  border: 1px solid rgb(34, 170, 255);
}

.grid-item:hover,
.grid-item.active {
  background: rgb(34, 170, 255);
}

.profile-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.profile-card {
  display: flex;
  align-items: center;
  padding: 25px;
  background: linear-gradient(135deg, #fff, #d8e8f3);
  border-radius: 10px;
  border: 1px solid #e3f2fd;
  transition: transform 0.3s, box-shadow 0.3s;
}

.profile-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 11, 0.228);
}

.profile-icon {
  font-size: 36px;
  margin-right: 20px;
  transition: font-size 0.3s ease;
}

.profile-info h3 {
  font-size: 18px;
  color: rgb(10, 0, 0);
  margin: 0 0 10px 0;
  font-weight: 500;
  transition: font-size 0.3s ease;
}

.status-text {
  font-size: 22px;
  font-weight: 600;
  color: var(--text-dark);
  margin: 0 0 8px 0;
  transition: font-size 0.3s ease;
}

.status-text.verified {
  color: var(--accent-green);
}

.status-text.pending {
  color: var(--warning);
}

.status-text.error {
  color: var(--error);
}

.status-date {
  font-size: 16px;
  color: var(--text-light);
  font-weight: 500;
  transition: font-size 0.3s ease;
}

/* 老年版个人信息样式 */
.elderly-mode .profile-icon {
  font-size: 48px;
  margin-right: 25px;
}

.elderly-mode .profile-info h3 {
  font-size: 22px;
  margin: 0 0 12px 0;
}

.elderly-mode .status-text {
  font-size: 28px;
  margin: 0 0 10px 0;
}

.elderly-mode .status-date {
  font-size: 20px;
}

/* 加载和错误状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e9ecef;
  border-top: 4px solid var(--primary-yellow);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-container p {
  font-size: 18px;
  color: var(--text-light);
  margin: 0;
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.error-message {
  font-size: 18px;
  color: var(--error);
  margin: 0 0 20px 0;
}

.retry-btn {
  padding: 12px 24px;
  background: var(--primary-yellow);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.3s ease;
}

.retry-btn:hover {
  background: var(--primary-yellow-dark);
}

/* 功能模块样式 */
.grid-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.grid-item {
  border: 1px solid var(--primary-yellow-dark);
  padding: 15px;
  border-radius: 12px;
  background-color: var(--primary-yellow);
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s, background 0.2s;
  box-shadow: 0 2px 8px rgba(10, 10, 10, 0.315);
}

.grid-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 16px rgba(7, 7, 7, 0.094);
  background:white;
}

.grid-item h3 {
  margin-top: 0;
  color: #222;
}

.grid-item p {
  font-size: 0.9em;
  color: #333;
  line-height: 1.4;
}

/* 老年版功能模块样式 */
.elderly-mode .item-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.elderly-mode .grid-item h3 {
  font-size: 30px;
  margin: 0 0 15px 0;
}

.elderly-mode .grid-item p {
  font-size: 22px;
  line-height: 1.8;
}

/* 快速操作样式 */
.action-buttons {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
  color: blue;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 28px;
  border: none;
  border-radius: 10px;
  font-size: 18px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  color: blue;
}

/* 老年版快速操作按钮样式 */
.elderly-mode .action-btn {
  padding: 20px 35px;
  font-size: 22px;
  gap: 15px;
  border-radius: 12px;
}

.action-btn.primary {
  background: rgb(34, 170, 255);
  color: white;
}

.action-btn.primary:hover {
  background: rgb(34, 170, 255);
  transform: translateY(-2px);
}

.action-btn.secondary {
  background: #0284f6;
  color: white;
}

.action-btn.secondary:hover {
  background: #028df7;
  transform: translateY(-2px);
}

.action-btn.tertiary {
  background: rgb(34, 170, 255);
  color: rgb(34, 170, 255);
  border: 1px solid rgb(34, 170, 255);
}

.action-btn.tertiary:hover {
  background: rgb(34, 170, 255);
  color: white;
  transform: translateY(-2px);
}

.btn-icon {
  font-size: 16px;
  transition: font-size 0.3s ease;
}

.elderly-mode .btn-icon {
  font-size: 20px;
}

/* 社区公告样式 */
.announcement-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.announcement-item {
  display: flex;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid rgb(34, 170, 255);
  transition: transform 0.2s;
}

.announcement-item:hover {
  transform: translateX(5px);
}

.announcement-date {
  font-size: 12px;
  color: var(--text-light);
  font-weight: 500;
  min-width: 80px;
  margin-right: 20px;
  padding-top: 2px;
  transition: font-size 0.3s ease;
}

.announcement-content h4 {
  font-size: 16px;
  color: var(--text-dark);
  margin: 0 0 8px 0;
  font-weight: 600;
  transition: font-size 0.3s ease;
}

.announcement-content p {
  font-size: 14px;
  color: var(--text-light);
  margin: 0;
  line-height: 1.5;
  transition: font-size 0.3s ease;
}

/* 老年版公告样式 */
.elderly-mode .announcement-date {
  font-size: 16px;
  min-width: 100px;
  margin-right: 25px;
}

.elderly-mode .announcement-content h4 {
  font-size: 20px;
  margin: 0 0 10px 0;
}

.elderly-mode .announcement-content p {
  font-size: 18px;
  line-height: 1.8;
}

/* 老年版布局调整 */
.elderly-mode .dashboard-content {
  gap: 50px;
}

.elderly-mode .profile-grid {
  gap: 25px;
}

.elderly-mode .grid-container {
  gap: 25px;
}

.elderly-mode .profile-card {
  padding: 30px;
}

.elderly-mode .grid-item {
  padding: 30px;
}

.elderly-mode .function-section,
.elderly-mode .profile-section,
.elderly-mode .quick-actions,
.elderly-mode .announcements {
  padding: 40px;
}

.elderly-mode .action-buttons {
  gap: 20px;
}

.elderly-mode .announcement-list {
  gap: 20px;
}

.elderly-mode .announcement-item {
  padding: 25px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .mode-toggle {
    top: 60px;
    right: 15px;
  }

  .toggle-btn {
    padding: 10px 16px;
    font-size: 14px;
  }

  .toggle-icon {
    font-size: 16px;
  }

  .toggle-text {
    font-size: 12px;
  }

  .dashboard-header h1 {
    font-size: 24px;
  }

  .profile-grid {
    grid-template-columns: 1fr;
  }

  .grid-container {
    grid-template-columns: 1fr;
  }

  .action-buttons {
    flex-direction: column;
  }

  .action-btn {
    justify-content: center;
  }

  .announcement-item {
    flex-direction: column;
  }

  .announcement-date {
    margin-right: 0;
    margin-bottom: 10px;
  }
}

/* 黄色按钮通用样式 */
.blue-btn, .btn, button, .action-btn, .primary, .secondary, .tertiary {
  background: rgb(250, 250, 250) !important;
  color: #222 !important;
  border: none !important;
  border-radius: 8px !important;
  padding: 10px 22px !important;
  font-weight: 600 !important;
  margin-top: 10px;
  cursor: pointer;
  transition: background 0.2s;
}

.blue-btn:hover, .btn:hover, button:hover, .action-btn:hover, .primary:hover, .secondary:hover, .tertiary:hover {
  background: rgb(2, 152, 245) !important;
  color: #111 !important;
}

body, .dashboard-content, .profile-section, .function-section, .quick-actions, .announcements {
  color: var(--text-dark);
}
</style>