package com.hfut.xiaozu.archive.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-07-02
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class VehicleVO {

    /**
     * 车位编号
     */
    private String spaceNumber;

    /**
     * 车位类型: 1-固定车位 2-临时车位
     */
    private Integer spaceType;

    private List<Long> houseIds = new ArrayList<>();

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 所属用户ID
     */
    private Long userId;

    /**
     * 车牌号码
     */
    private String plateNumber;

    /**
     * 车辆品牌
     */
    private String brand;

    /**
     * 车辆颜色
     */
    private String color;

    /**
     * 是否小区内车辆 0-否 1-是
     */
    private Integer isInCommunity;

}
